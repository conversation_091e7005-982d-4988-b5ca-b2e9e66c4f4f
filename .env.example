PORT= '4001'
TZ= 'UTC'
REQUEST_TIMEOUT= '180000'
#Swagger Config
SWAGGER_TITLE= 'APE PLATFORM API'
SWAGGER_DESCRIPTION= 'The APE PLATFORM API'
SWAGGER_VERSION= '1.0'
# Primary Database
DB_PRIMARY_TYPE= 'postgres'
DB_PRIMARY_HOST= 'ape-postgre...................'
DB_PRIMARY_PORT= '5432'
DB_PRIMARY_USERNAME= 'ape-platform-dev'
DB_PRIMARY_PASSWORD= 'ape2$plat...................'
DB_PRIMARY_DATABASE= 'ape-platform...................'
DB_PRIMARY_SYNCHRONIZE= 'true'
DB_PRIMARY_SSL= 'true'
DB_PRIMARY_SSL_REJECT_UNAUTHORIZED= 'false'
# JWT HS256 config
JWT_SECRET= '/q5zjNG6W0cbEdseJEySM...................'
JWT_EXPIRY= '100d'
JWT_REFRESH_TOKEN_SECRET= '/A5zjN26W0cbEdseJEDs...................'
JWT_REFRESH_TOKEN_EXPIRY= '300d'
# APE AUTH
APE_SSO_URL="https://authenticator..................."
APE_CLIENT_ID="PMS_3aa290fe86c44..................."
APE_CLIENT_SECRET="d12819613daf395b9ccfed0dddc6..................."
APE_API_KEY="$yHvX4EN&i*GC_V()DFER4A%TR!Vx4Ya#............."
APE_CALLBACK_URL=http://localhost:4001/api/...................
# GOOGLE MAIL 
SERVICE_SEND_MAIL=gmail
GOOGLE_MAIL_USER=chucattuo...................
GOOGLE_APP_PASSWORD='jart mi...................'

# AWS S3 CONFIG
LINK_UPLOAD_S3=ape-b...................
AWS_S3_BUCKET_NAME=ape-d...................
AWS_S3_ACCESS_KEY_ID=AKIARD...................
AWS_S3_SECRET_ACCESS_KEY=/DIKQa//iyYZU...................

# REDIS
REDIS_URL='redis://default:dqZ7xy4bv8c6p2...................