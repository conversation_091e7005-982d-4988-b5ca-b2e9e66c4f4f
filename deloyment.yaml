#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
    name: ape-platform-ai-api-dev-config
    namespace: ape-platform-dev
data:
    NODE_ENV: 'production'
    PORT: '80'
    TZ: 'UTC'
    REQUEST_TIMEOUT: '180000'
    #Swagger Config
    SWAGGER_TITLE: 'APE PLATFORM API'
    SWAGGER_DESCRIPTION: 'The APE PLATFORM API'
    SWAGGER_VERSION: '1.0'
    # Primary Database
    DB_PRIMARY_TYPE: 'postgres'
    DB_PRIMARY_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
    DB_PRIMARY_PORT: '5432'
    DB_PRIMARY_USERNAME: 'ape-platform-dev'
    DB_PRIMARY_PASSWORD: 'ape2$platform3#@dev'
    DB_PRIMARY_DATABASE: 'ape-platform-pms-ai-dev'
    DB_PRIMARY_SYNCHRONIZE: 'true'
    DB_PRIMARY_SSL: 'true'
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: 'false'
    # JWT HS256 config
    JWT_SECRET: '/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo: '
    JWT_EXPIRY: '100d'
    JWT_REFRESH_TOKEN_SECRET: '/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi: '
    JWT_REFRESH_TOKEN_EXPIRY: '300d'
    # APE AUTH
    APE_SSO_URL: 'https://authenticator-dev.apetechs.co'
    APE_CLIENT_ID: 'PMSAI001_3e1191e3154b43c2b7541a0e953b12d2'
    APE_CLIENT_SECRET: '43a88ef60abccbb7c534f95832214cb8fcbce9fc6e7b83db24577d9643cdc92e'
    APE_API_KEY: '$yHvX4EN&i*GC_V()DFER4A%TR!Vx4Ya#GOq@jyL<DsXFawsS(j=txdj12KEw@'
    APE_CALLBACK_URL: 'https://platform-api-dev.apetechs.co/api/client/auth/ape/callback'
    # GOOGLE MAIL
    SERVICE_SEND_MAIL: 'gmail'
    GOOGLE_MAIL_USER: '<EMAIL>'
    GOOGLE_APP_PASSWORD: 'jart miao zufo xsca'

    # AWS S3 CONFIGs
    LINK_UPLOAD_S3: 'ape-bl-dev'
    AWS_S3_BUCKET_NAME: 'ape-devs-co'
    AWS_S3_ACCESS_KEY_ID: '********************'
    AWS_S3_SECRET_ACCESS_KEY: '/DIKQa//iyYZUvucHau/cRItB+LCkQ76XWspfrcO'

    # Redis
    REDIS_URL: 'redis://default:<EMAIL>:14573'

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
    name: ape-platform-ai-api-dev
    namespace: ape-platform-dev
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ape-platform-ai-api-dev
    template:
        metadata:
            labels:
                app: ape-platform-ai-api-dev
        spec:
            containers:
                - name: ape-platform-ai-api-dev
                  image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-platform-ai-api-dev:latest
                  ports:
                      - containerPort: 80
                  envFrom:
                      - configMapRef:
                            name: ape-platform-ai-api-dev-config
                  volumeMounts:
                      - mountPath: /etc/localtime
                        name: tz-config
            volumes:
                - name: tz-config
                  hostPath:
                      path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
    name: ape-platform-ai-api-dev
    namespace: ape-platform-dev
    labels:
        run: ape-platform-ai-api-dev
spec:
    type: ClusterIP
    ports:
        - port: 80
          protocol: TCP
          targetPort: 80
    selector:
        app: ape-platform-ai-api-dev
