name: <PERSON><PERSON> and <PERSON>oy develop

on:
  push:
    branches: [production]

env:
  DMS_API_SETTING_ECR_REPOSITORY: ape-chain-api-prod

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-access-key-id: ${{ secrets.AWS_APE_EKS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_APE_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_APE_EKS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/$DMS_API_SETTING_ECR_REPOSITORY:$IMAGE_TAG -f ./Dockerfile.develop .
          docker push $ECR_REGISTRY/$DMS_API_SETTING_ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$DMS_API_SETTING_ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      # - name: Update kubeconfig
      #   run: |
      #     aws eks update-kubeconfig --name ${{ secrets.AWS_APE_EKS_CLUSTER_NAME }} --region ${{ secrets.AWS_APE_EKS_REGION }}

      # - name: Run kubectl command
      #   run: |
      #     kubectl delete -f deloyment.yaml
      #     kubectl apply -f deloyment.yaml
