apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ape-crm-dev-ingress-host
  namespace: ape-crm-dev
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
spec:
  rules:
    - host: ape-crm-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-crm-dev
                port:
                  number: 80
