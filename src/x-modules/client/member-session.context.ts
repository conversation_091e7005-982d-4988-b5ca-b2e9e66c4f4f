import { RequestContext } from '~/@core/context';
import { KEY_SESSION_CONTEXT } from '~/common/constants';
import { MemberSessionDto } from '~/x-modules/client/member-auth/dto';

export class MemberSessionContext {
    get sessionData() {
        return RequestContext.getAttribute<MemberSessionDto>(KEY_SESSION_CONTEXT.MEMBER_SESSION);
    }

    get accessToken() {
        return this.sessionData?.accessToken;
    }
    get memberId() {
        return this?.sessionData?.sub;
    }
    get username() {
        return this.sessionData?.username;
    }
    get tenantId() {
        return this.sessionData?.tenantId;
    }
}

export const memberSessionContext = new MemberSessionContext();
