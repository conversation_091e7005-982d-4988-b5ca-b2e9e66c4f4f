import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { RefixModule } from '../config-module';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { lazyLoadClasses } from '~/utils/tech.util';
import { MemberMiddleware } from './member.middleware';
const controllers = lazyLoadClasses(__dirname, ['.controller']);
const services = lazyLoadClasses(__dirname, ['.service']);

@ChildModule({
    prefix: RefixModule.client,
    imports: [PrimaryRepoModule],
    providers: [...services],
    controllers: [...controllers],
})
export class ClientModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(MemberMiddleware)
            .exclude(
                { path: `${RefixModule.client}/auth/ape`, method: RequestMethod.GET },
                { path: `${RefixModule.client}/auth/ape/callback`, method: RequestMethod.GET },
                { path: `${RefixModule.client}/comment/list`, method: RequestMethod.POST },
            )
            .forRoutes({ path: `${RefixModule.client}/*path`, method: RequestMethod.ALL });
    }
}
