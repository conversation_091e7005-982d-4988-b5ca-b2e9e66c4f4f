import { ApiProperty } from '@nestjs/swagger';

export class SupplierReq {
    tenantId: string;
    supplierId: string;
}

export class SupplierInsertReq {
    @ApiProperty({ description: 'Tên' })
    name: string;

    @ApiProperty({ description: 'Đ<PERSON>a chỉ' })
    address: string;

    @ApiProperty({ description: 'Số điện thoại' })
    phone: string;

    @ApiProperty({ description: 'Email' })
    email: string;

    @ApiProperty({ description: 'Mã số thuế' })
    taxCode: string;

    @ApiProperty({ description: 'Logo' })
    logoUrl: string;

    @ApiProperty({ description: 'Username' })
    username: string;

    @ApiProperty({ description: 'Website' })
    password: string;
}

export class SupplierUpdateReq {
    @ApiProperty({ description: 'Id nhà cung cấp' })
    id: string;

    @ApiProperty({ description: 'Mã tenant' })
    tenantId?: string;

    @ApiProperty({ description: 'Tên nhà cung cấp' })
    fullName?: string;

    @ApiProperty({ description: 'Địa chỉ' })
    address?: string;

    @ApiProperty({ description: 'Số điện thoại' })
    phone?: string;

    @ApiProperty({ description: 'Email' })
    email?: string;

    @ApiProperty({ description: 'Mã số thuế' })
    taxCode?: string;

    @ApiProperty({ description: 'Logo' })
    logoUrl?: string;

    @ApiProperty({ description: 'Website' })
    websiteUrl?: string;

    // Law
    @ApiProperty({ description: 'Loại hình hồ sơ' })
    profileTypes?: string;

    @ApiProperty({ description: 'Loại hình công ty' })
    companyTypes?: string;

    @ApiProperty({ description: 'Ngày cấp ĐKKD' })
    dateOfIssue?: string;

    @ApiProperty({ description: 'Nơi cấp ĐKKD' })
    placeOfIssue?: string;

    @ApiProperty({ description: 'Tên hồ sơ' })
    profileName?: string;

    @ApiProperty({ description: 'Địa chỉ theo ĐKKD' })
    addressOfIssue?: string;

    @ApiProperty({ description: 'Địa chỉ giao dịch' })
    dealAddress?: string;

    @ApiProperty({ description: 'Vốn điều lệ' })
    capital?: string;

    @ApiProperty({ description: 'Tài sản cố định' })
    assets?: string;

    @ApiProperty({ description: 'Fax' })
    fax?: string;

    @ApiProperty({ description: 'Ngày thành lập' })
    dateOfEstablishment?: string;

    @ApiProperty({ description: 'Họ và tên người đại diện' })
    legalRepName?: string;

    @ApiProperty({ description: 'Chức vụ' })
    legalRepPosition?: string;

    @ApiProperty({ description: 'Số điện thoại người đại diện' })
    legalRepPhone?: string;

    @ApiProperty({ description: 'Email người đại diện' })
    legalRepEmail?: string;

    @ApiProperty({ description: 'File pháp lý' })
    legalFile?: string[];

    // Opacity
    @ApiProperty({ description: 'Lĩnh vực kinh doanh' })
    businessAreas?: any[];

    @ApiProperty({
        description: 'Chứng nhận giải thưởng',
        example: [{ name: 'name', url: 'url' }],
        required: false,
    })
    certificatesFiles?: any[];

    @ApiProperty({
        description: 'Doanh thu gần đây',
        example: [{ year: 'year', value: 'value' }],
        required: false,
    })
    recentRevenue?: any[];

    @ApiProperty({ description: 'Báo cáo tài chính' })
    financialReports?: any[];

    @ApiProperty({ description: 'Tài liệu tham khảo' })
    referenceDocuments?: any[];
}
