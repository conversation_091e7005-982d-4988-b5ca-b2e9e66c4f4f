import { Def<PERSON>ontroller, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { SupplierService } from './supplier.service';
import { Body } from '@nestjs/common';
import { SupplierInsertReq, SupplierReq, SupplierUpdateReq } from './dto/supplier.dto';

@DefController('supplier')
export class SupplierController {
    constructor(private readonly supplierSevice: SupplierService) {}

    @DefGet('list')
    async get() {
        return await this.supplierSevice.list();
    }

    @DefPost('detail')
    async detail() {
        return await this.supplierSevice.detail();
    }

    @DefPost('insert')
    async insert(@Body() data: SupplierInsertReq) {
        return await this.supplierSevice.insert(data);
    }

    @DefPost('update')
    async update(@Body() data: SupplierUpdateReq) {
        return await this.supplierSevice.update(data);
    }

    @DefPost('update-avatar')
    async updateAvatar(@Body() data: { url: string }) {
        return await this.supplierSevice.updateAvatar(data);
    }

    
}
