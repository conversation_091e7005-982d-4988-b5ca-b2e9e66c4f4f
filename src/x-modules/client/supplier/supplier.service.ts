import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ServiceRepo, SupplierRepo } from '~/domains/primary';
import { SupplierInsertReq, SupplierReq, SupplierUpdateReq } from './dto/supplier.dto';
import { memberSessionContext } from '~/x-modules/client/member-session.context';
import { SupplierOpacityRepo } from '~/domains/primary/supplier-opacity/supplier-opacity.repo';
import { SupplierLawRepo } from '~/domains/primary/supplier-law/supplier-law.repo';
import { DeepPartial, In } from 'typeorm';
import { SupplierOpacityEntity } from '~/domains/primary/supplier-opacity/supplier-opacity.entity';
import { SupplierLawEntity } from '~/domains/primary/supplier-law/supplier-law.entity';
import { configEnv } from '~/@config/env';
import { optionalApiConnector } from '~/connectors';
const { APE_SSO_URL, APE_CLIENT_ID, APE_CLIENT_SECRET } = configEnv();
@Injectable()
export class SupplierService {
    constructor(
        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(SupplierLawRepo)
        private readonly supplierLawRepo: SupplierLawRepo,

        @InjectRepo(SupplierOpacityRepo)
        private readonly supplierOpacityRepo: SupplierOpacityRepo,

        @InjectRepo(ServiceRepo)
        private readonly serviceRepo: ServiceRepo,
    ) {}
    async list() {
        return await this.supplierRepo.find();
    }

    async detail() {
        const { tenantId } = memberSessionContext;
        const supplier = await this.supplierRepo.findOne({
            where: {
                tenantId: tenantId,
            },
        });
        if (!supplier) {
            return { message: 'Supplier not found' };
        }

        const supplierLaw = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        const supplierOpacity = await this.supplierOpacityRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        let category: any[] = [];
        if (supplierOpacity?.businessAreas) {
            category = await this.serviceRepo.find({
                where: { id: In(supplierOpacity?.businessAreas) },
            });
        }

        return {
            ...supplier,
            ...supplierLaw,
            ...supplierOpacity,
            id: supplier.id,
            category,
        };
    }

    async insert(data: SupplierInsertReq) {
        const supplier = await this.supplierRepo.create(data);
        return await this.supplierRepo.save(supplier);
    }

    async update(data: SupplierUpdateReq) {
        const { tenantId } = memberSessionContext;
        const updateEndpoint = `${APE_SSO_URL}/api/public/account/sync-update-tenant`;
        const supplier = await this.supplierRepo.findOne({
            where: {
                tenantId: tenantId,
            },
        });

        if (!supplier) {
            return { message: 'Supplier not found' };
        }

        const supplierLaw = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        const dataLaw: DeepPartial<SupplierLawEntity> = {
            profileTypes: data.profileTypes,
            companyTypes: data.companyTypes,
            dateOfIssue: data.dateOfIssue,
            placeOfIssue: data.placeOfIssue,
            profileName: data.profileName,
            addressOfIssue: data.addressOfIssue,
            dealAddress: data.dealAddress,
            capital: data.capital,
            fax: data.fax,
            supplierId: supplier.id,
            dateOfEstablishment: data.dateOfEstablishment,
            legalRepName: data.legalRepName,
            legalRepPosition: data.legalRepPosition,
            legalRepPhone: data.legalRepPhone,
            legalRepEmail: data.legalRepEmail,
            legalFile: data.legalFile,
        };

        if (supplierLaw) {
            await this.supplierLawRepo.update(supplierLaw.id, dataLaw);
        } else {
            await this.supplierLawRepo.save(dataLaw);
        }

        const supplierOpacity = await this.supplierOpacityRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        const dataOpacity: DeepPartial<SupplierOpacityEntity> = {
            businessAreas: data.businessAreas,
            certificatesFiles: data.certificatesFiles,
            recentRevenue: data.recentRevenue,
            financialReports: data.financialReports,
            referenceDocuments: data.referenceDocuments,
            supplierId: supplier.id,
        };

        if (supplierOpacity) {
            await this.supplierOpacityRepo.update(supplierOpacity.id, dataOpacity);
        } else {
            await this.supplierOpacityRepo.save(dataOpacity);
        }

        const dataSup = {
            fullName: data.fullName,
            email: data.email,
            phone: data.phone,
            address: data.address,
            taxCode: data.taxCode,
            logoUrl: data.logoUrl,
            websiteUrl: data.websiteUrl,
        };
        const syncAuth = {
            name: data.fullName,
            phone: data.phone,
            email: data.email,
            website: data.websiteUrl,
            tenantId: tenantId
        }
        const headers = {
            'Content-Type': 'application/json',
            'x-api-key': process.env.APE_API_KEY,    
        };
        await optionalApiConnector.post(updateEndpoint, syncAuth, { headers });
        await this.supplierRepo.update(supplier.id, dataSup);

        return { message: 'Update supplier success' };
    }

    async updateAvatar(data: { url: string }) {
        const { tenantId } = memberSessionContext;
        const supplier = await this.supplierRepo.findOne({
            where: { tenantId: tenantId },
        });

        if (!supplier) {
            return { message: 'Supplier not found' };
        }

        await this.supplierRepo.update(supplier.id, { logoUrl: data.url });

        return { message: 'Update avatar success' };
    }

  
}
