import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { CommentRepo } from '~/domains/primary/comment/comment.repo';
import { CreateCommentDto, ListCommentDto } from './dto/comment.dto';
import { SupplierRepo } from '~/domains/primary';

@Injectable()
export class CommentService {
    constructor(
        @InjectRepo(CommentRepo)
        private readonly commentRepo: CommentRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,
    ) {}

    // insert comment
    async insert(data: CreateCommentDto) {
        try {
            const newComment = this.commentRepo.create(data);
            return this.commentRepo.save(newComment);
        } catch (error) {
            console.log(error);
        }
    }

    async list(data: ListCommentDto) {
        const cmt = await this.commentRepo.find({
            where: { itemId: data.itemId },
            order: { createdDate: 'DESC' },
        });

        const idsSupplier = cmt.map((item) => item.supplierId);
        const listSupplier = await this.supplierRepo.findByIds(idsSupplier);
        const listSupplierMap = listSupplier.reduce((acc, item) => {
            acc[item.id] = item;
            return acc;
        }, {});

        return cmt.map((item) => {
            return {
                ...item,
                supplier: listSupplierMap[item.supplierId],
            };
        });

    }
}
