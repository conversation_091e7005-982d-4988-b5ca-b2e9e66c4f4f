import { Def<PERSON><PERSON>roller, DefPost } from "nestjs-typeorm3-kit";
import { CommentService } from "./comment.service";
import { Body } from "@nestjs/common";
import { CreateCommentDto, ListCommentDto } from "./dto/comment.dto";

@DefController('comment')
export class CommentController {
    constructor(
        private readonly commentService: CommentService,
    ) { }

    @DefPost('insert')
    async insert(@Body() data: CreateCommentDto) {
        return this.commentService.insert(data);
    }

    @DefPost('list')
    async list(@Body() data: ListCommentDto) {
        return this.commentService.list(data);
    }
}