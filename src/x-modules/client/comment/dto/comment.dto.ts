import { ApiProperty } from '@nestjs/swagger';
export class CreateCommentDto {
  @ApiProperty({ description: 'comment content', required: false })
  content: string;

  @ApiProperty({ description: 'Id nhà cung cấp', required: true })
  supplierId: string;

  @ApiProperty({ description: 'Id sản phẩm', required: true })
  itemId: string;

  @ApiProperty({ description: 'Đánh giá', required: false })
  rate: number;
}

export class ListCommentDto {
  @ApiProperty({ description: 'Id sản phẩm', required: true })
  itemId: string;
}
