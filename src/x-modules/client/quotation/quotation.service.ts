import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ItemRepo, QuotationRepo, SupplierRepo } from '~/domains/primary';
import {
    HistoryRequestQuoteDto,
    QuotationDto,
    QuoteDto,
    RequestQuoteDto,
} from './dto/quotation.dto';
import { memberSessionContext } from '~/x-modules/client/member-session.context';
import { NSQuotation } from '~/common/enums/quotation.enum';
import { In } from 'typeorm';
import { SupplierLawRepo } from '~/domains/primary/supplier-law/supplier-law.repo';
import { NotificationService } from '../notification/notification.service';
import { NotificationGateway } from '../notification/notification.gateway.service';

@Injectable()
export class QuotationService {
    constructor(
        @InjectRepo(QuotationRepo)
        private readonly quotationRepo: QuotationRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(ItemRepo)
        private readonly itemRepo: ItemRepo,

        @InjectRepo(SupplierLawRepo)
        private readonly supplierLawRepo: SupplierLawRepo,
        private readonly notificationService: NotificationService,
        private readonly notificationGateway: NotificationGateway,
    ) {}

    // gửi yêu cầu báo giá
    async requestQuote(data: RequestQuoteDto) {
        const code = await this.generateCodeRequestQuote();
        const { tenantId } = memberSessionContext;
        if (tenantId) {
            const supplier = await this.supplierRepo.findOne({
                where: {
                    tenantId,
                },
            });

            if (!supplier) {
                return {
                    message: 'Bạn không phải là nhà cung cấp',
                };
            }
            data.supplierRequestId = supplier.id;
        }
        data.requestCode = code;

        const quotation = this.quotationRepo.create(data);
        await this.quotationRepo.save(quotation);

        // Tạo thông báo cho nhà cung cấp nhận yêu cầu báo giá
        if (data.supplierReceiverId) {
            try {
                // Lấy thông tin người gửi
                const requesterSupplier = await this.supplierRepo.findOne({
                    where: { id: data.supplierRequestId },
                });

                // Lấy thông tin sản phẩm
                const item = await this.itemRepo.findOne({
                    where: { id: data.itemId },
                });

                const notification = await this.notificationService.createQuoteNotification(
                    {
                        ...quotation,
                        itemName: item?.name,
                    },
                    data.supplierReceiverId,
                    requesterSupplier.id,
                );

                // Gửi thông báo real-time qua socket
                this.notificationGateway.sendNotificationToUser(
                    data.supplierReceiverId,
                    notification,
                );
            } catch (error) {
                console.error('Error creating quote notification:', error);
            }
        }

        return {
            message: 'success',
            data: quotation,
        };
    }

    // Danh sách báo giá(KH mua)
    async getQuoteReceived(params: HistoryRequestQuoteDto) {
        const whereCon: any = {};
        const { tenantId } = memberSessionContext;
        if (tenantId) {
            const supplier = await this.supplierRepo.findOne({
                where: {
                    tenantId,
                },
            });
            if (!supplier) {
                return {
                    message: 'Bạn không phải là nhà cung cấp',
                };
            }
            whereCon.supplierReceiverId = supplier.id;
        }
        const quotations = await this.quotationRepo.findPagination(
            {
                where: whereCon,
                order: { createdDate: 'DESC' },
            },
            params,
        );

        const idsItem = quotations.data.map(item => item.itemId);

        const items = await this.itemRepo.findBy({
            id: In(idsItem),
        });

        const idsSupplier = quotations.data.map(item => item.supplierRequestId);
        const suppliers = await this.supplierRepo.findBy({
            id: In(idsSupplier),
        });

        quotations.data = quotations.data.map(q => {
            const itemDetail = items.find(item => item.id === q.itemId);
            return {
                ...q,
                itemName: itemDetail.name,
                itemCode: itemDetail.code,
                supplierRequestName: suppliers.find(supplier => supplier.id === q.supplierRequestId)?.fullName || null,
                supplierRequestPhone: suppliers.find(supplier => supplier.id === q.supplierRequestId)?.phone || null,
                supplierRequestEmail: suppliers.find(supplier => supplier.id === q.supplierRequestId)?.email || null,
                supplierRequestAddress: suppliers.find(supplier => supplier.id === q.supplierRequestId)?.address || null,
            };
        });

        return quotations;
    }

    // danh sách yêu cầu báo giá(mình mua)
    async getQuoteRequest(params: HistoryRequestQuoteDto) {
        const whereCon: any = {};
        const { tenantId } = memberSessionContext;
        if (tenantId) {
            const supplier = await this.supplierRepo.findOne({
                where: {
                    tenantId,
                },
            });
            if (!supplier) {
                return {
                    message: 'Bạn không phải là nhà cung cấp',
                };
            }
            whereCon.supplierRequestId = supplier.id;
        }
        const quotations = await this.quotationRepo.findPagination(
            {
                where: whereCon,
                order: { createdDate: 'DESC' },
            },
            params,
        );

        const idsItem = quotations.data.map(item => item.itemId);

        const items = await this.itemRepo.findBy({
            id: In(idsItem),
        });

        const idsSupplier = quotations.data.map(item => item.supplierReceiverId);
        const suppliers = await this.supplierRepo.findBy({
            id: In(idsSupplier),
        });

        quotations.data = quotations.data.map(q => {
            const itemDetail = items.find(item => item.id === q.itemId);
            const supplierDetail = suppliers.find(supplier => supplier.id === q.supplierReceiverId);
            return {
                ...q,
                supplierReceiverName: supplierDetail?.fullName || null,
                itemName: itemDetail.name,
                itemCode: itemDetail.code,
            };
        });

        return quotations;
    }

    // lịch sử báo giá
    async getHistoryQuote(params: HistoryRequestQuoteDto) {
        const whereCon: any = {};
        const { tenantId } = memberSessionContext;
        if (tenantId) {
            const supplier = await this.supplierRepo.findOne({
                where: {
                    tenantId,
                },
            });
            if (!supplier) {
                return {
                    message: 'Bạn không phải là nhà cung cấp',
                };
            }
            whereCon.supplierReceiverId = supplier.id;
        }
        whereCon.status = NSQuotation.EStatus.QUOTED;
        const quotations = await this.quotationRepo.findPagination(
            {
                where: whereCon,
                order: { createdDate: 'DESC' },
            },
            params,
        );
        const idsItem = quotations.data.map(item => item.itemId);
        const items = await this.itemRepo.findBy({
            id: In(idsItem),
        });
        const idsSupplier = quotations.data.map(item => item.supplierRequestId);
        const suppliers = await this.supplierRepo.findBy({
            id: In(idsSupplier),
        });
        quotations.data = quotations.data.map(q => {
            const itemDetail = items.find(item => item.id === q.itemId);
            const supplierDetail = suppliers.find(supplier => supplier.id === q.supplierRequestId);
            return {
                ...q,
                itemName: itemDetail?.name,
                itemCode: itemDetail?.code,
                supplierRequestName: supplierDetail?.fullName || null,
            };
        });

        return quotations;
    }

    // detail yêu cầu báo giá
    async getDetailRequestQuote(data: QuotationDto) {
        const quotation = await this.quotationRepo.findOne({
            where: {
                id: data.id,
            },
        });

        const item = await this.itemRepo.findOne({
            where: {
                id: quotation.itemId,
            },
        });

        const supplierReceiver = await this.supplierRepo.findOne({
            where: {
                id: quotation.supplierReceiverId,
            },
        });

        const supplierRequest = await this.supplierRepo.findOne({
            where: {
                id: quotation.supplierRequestId,
            },
        });

        const supplierLawRequest = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplierRequest.id,
            },
        });

        const supplierLawReceiver = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplierReceiver.id,
            },
        });
        return {
            ...quotation,
            itemName: item?.name,
            itemCode: item?.code,
            supplierReceiverName: supplierReceiver?.fullName,
            supplierReceiverTaxCode: supplierReceiver?.taxCode,
            supplierReceiverPhone: supplierReceiver?.phone,
            supplierReceiverEmail: supplierReceiver?.email,
            supplierReceiverAddress: supplierReceiver?.address,
            supplierRequestName: supplierRequest?.fullName,
            supplierRequestPhone: supplierRequest?.phone,
            supplierRequestEmail: supplierRequest?.email,
            supplierRequestAddress: supplierRequest?.address,
            supplierRequestTaxCode: supplierRequest?.taxCode,
            legalRepNameRequest: supplierLawRequest?.legalRepName,
            legalRepPositionRequest: supplierLawRequest?.legalRepPosition,
            legalRepPhoneRequest: supplierLawRequest?.legalRepPhone,
            legalRepEmailRequest: supplierLawRequest?.legalRepEmail,
            legalRepNameReceiver: supplierLawReceiver?.legalRepName,
            legalRepPositionReceiver: supplierLawReceiver?.legalRepPosition,
            legalRepPhoneReceiver: supplierLawReceiver?.legalRepPhone,
            legalRepEmailReceiver: supplierLawReceiver?.legalRepEmail,
        };
    }

    // Báo giá
    async quote(data: QuoteDto) {
        const code = await this.generateCodeQuote();
        data.quotationCode = code;
        data.status = NSQuotation.EStatus.QUOTED;
        const quotation = await this.quotationRepo.update(data.id, data);

        // Sau khi tạo báo giá thành công, tạo thông báo cho người yêu cầu
        const quotationCreated = await this.quotationRepo.findOne({
            where: { id: data.id },
        });

        if (quotationCreated && quotationCreated.supplierRequestId) {
            try {
                // Lấy thông tin nhà cung cấp gửi báo giá
                const quoterSupplier = await this.supplierRepo.findOne({
                    where: { id: quotationCreated.supplierReceiverId },
                });

                // Lấy thông tin sản phẩm
                const item = await this.itemRepo.findOne({
                    where: { id: quotationCreated.itemId },
                });

                const notification = await this.notificationService.createQuoteNotification(
                    {
                        ...quotationCreated,
                        itemName: item?.name,
                        quotationCode: data.quotationCode,
                    },
                    quotationCreated.supplierRequestId,
                    quoterSupplier,
                );

                // Gửi thông báo real-time
                this.notificationGateway.sendNotificationToUser(
                    quotationCreated.supplierRequestId,
                    notification,
                );
            } catch (error) {
                console.error('Error creating quote response notification:', error);
            }
        }

        return {
            message: 'Quoted successfully!',
            data: quotation,
        };
    }

    // Từ chối báo giá
    async rejectQuote(data: QuoteDto) {
        data.status = NSQuotation.EStatus.CANCEL;
        const quotation = await this.quotationRepo.update(data.id, data);
        return {
            message: 'Rejected successfully!',
            data: quotation,
        };
    }

    private async generateCodeRequestQuote(): Promise<string> {
        const existingRequestQuote = await this.quotationRepo.find();
        const code = 'RQQ000' + (existingRequestQuote.length + 1);
        return code;
    }

    private async generateCodeQuote(): Promise<string> {
        const existingRequestQuote = await this.quotationRepo.find();
        const code = 'Q000' + (existingRequestQuote.length + 1);
        return code;
    }
}
