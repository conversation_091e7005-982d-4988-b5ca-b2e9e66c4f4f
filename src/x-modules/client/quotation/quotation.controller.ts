import { DefController, DefGet, DefPost } from "nestjs-typeorm3-kit";
import { QuotationService } from "./quotation.service";
import { Body, Query } from "@nestjs/common";
import { HistoryRequestQuoteDto, QuotationDto, QuoteDto, RequestQuoteDto } from "./dto/quotation.dto";

@DefController('quotation')
export class QuotationController {
    constructor(
        private readonly quotationService: QuotationService,
    ) { }

    // gửi yêu cầu báo giá
    @DefPost('request-quote')
    requestQuote(@Body() data: RequestQuoteDto) {
        return this.quotationService.requestQuote(data);
    }

    // danh sách yêu cầu báo giá(mình mua)
    @DefGet('get-quote-request')
    getQuoteRequest(@Query() data: HistoryRequestQuoteDto) {
        return this.quotationService.getQuoteRequest(data);
    }

    // Danh sách báo giá(KH mua)
    @DefGet('get-quote-received')
    getQuoteReceived(@Query() params: HistoryRequestQuoteDto) {
        return this.quotationService.getQuoteReceived(params);
    }

    // detail yêu cầu báo giá
    @DefPost('quotation_request_detail')
    getDetailRequestQuote(@Body() data: QuotationDto) {
        return this.quotationService.getDetailRequestQuote(data);
    }

    // Báo giá
    @DefPost('quote')
    quote(@Body() data: QuoteDto) {
        return this.quotationService.quote(data);
    }
    
}
