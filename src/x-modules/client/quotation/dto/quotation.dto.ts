import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";
import { PageRequest } from "~/@systems/utils";
import { NSQuotation } from "~/common/enums/quotation.enum";

export class RequestQuoteDto {
    @ApiProperty({ description: 'Mã yêu cầu báo giá' })
    requestCode: string;

    @ApiProperty({ description: 'ID nhà cung cấp' })
    @IsNotEmpty()
    supplierReceiverId: string;

    @ApiProperty({ description: 'ID bên yêu cầu báo giá' })
    supplierRequestId: string;

    @ApiProperty({ description: 'ID sản phẩm' })
    @IsNotEmpty()
    @IsString()
    itemId: string;

    @ApiProperty({ description: 'Số lượng' })
    @IsNotEmpty()
    quantity: number;

    @ApiProperty({ description: 'Đơn vị' })
    unit: string;

    @ApiProperty({ description: 'Đ<PERSON>a chỉ nhận hàng' })
    deliveryAddress: string;

    @ApiProperty({ description: '<PERSON><PERSON>y giao hàng' })
    deliveryDate: string;

    @ApiProperty({ description: 'Đi<PERSON>u kiện incoterm' })
    incoterm: string;

    @ApiProperty({ description: 'Điều khoản thanh toán' })
    paymentTerms: string;

    @ApiProperty({ description: 'Yêu cầu khác' })
    otherRequest: string;

    @ApiProperty({ description: 'Trạng thái', enum: NSQuotation.EStatus , default: NSQuotation.EStatus.NEW })
    status: string;
}

export class HistoryRequestQuoteDto extends PageRequest {
}

export class QuotationDto {
    @ApiProperty({ description: 'Mã báo giá' })
    id: string;
}


export class QuoteDto{
    @ApiProperty({ description: 'Mã yêu cầu báo giá' })
    id: string;

    @ApiProperty({ description: 'Mã báo giá' })
    quotationCode: string;

    @ApiProperty({description: 'File báo giá'})
    quotationFile: any;

    @ApiProperty({ description: 'Trạng thái' })
    status: string;

    @ApiProperty({ description: 'Ghi chú' })
    note: string;

    @ApiProperty({ description: 'Hạn báo giá' })
    deliveryCost: number;

    @ApiProperty({ description: 'Tổng tiền' })
    totalPrice: number;

    @ApiProperty({ description: 'Ngày giao hàng' })
    deliveryDate: string;
}