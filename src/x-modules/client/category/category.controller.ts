import { DefController, DefGet } from 'nestjs-typeorm3-kit';

import { CategoryService } from './category.service';

@DefController('category')
export class CategoryController {
    constructor(private readonly categoryService: CategoryService) {}

    @DefGet('get-all', {
        summary: 'Hàm lấy danh sách category',
    })
    async getAll() {
        return await this.categoryService.getAll();
    }
}
