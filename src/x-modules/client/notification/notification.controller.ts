import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { NotificationService } from './notification.service';
import { Body } from '@nestjs/common';
import { GetNotificationsDto, MarkAsReadDto } from './dto/notification.dto';

@DefController('notification')
export class NotificationController {
    constructor(private readonly notificationService: NotificationService) {}

    @DefPost('list')
    getNotifications(@Body() Body: GetNotificationsDto) {
        return this.notificationService.getNotifications(Body);
    }

    @DefPost('unread-count')
    getUnreadCount() {
        return this.notificationService.getUnreadCount();
    }

    @DefPost('mark-as-read')
    markAsRead(@Body() data: MarkAsReadDto) {
        return this.notificationService.markAsRead(data);
    }
}
