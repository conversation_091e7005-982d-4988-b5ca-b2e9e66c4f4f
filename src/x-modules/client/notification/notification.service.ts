import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { NotificationRepo, SupplierRepo } from '~/domains/primary';
import { CreateNotificationDto, GetNotificationsDto, MarkAsReadDto } from './dto/notification.dto';
import { memberSessionContext } from '~/x-modules/client/member-session.context';
import { NSNotification } from '~/common/enums/notification.enum';
import { In } from 'typeorm';
import { ChatMessageRepo } from '~/domains/primary/chat/chat-message.repo';

@Injectable()
export class NotificationService {
    constructor(
        @InjectRepo(NotificationRepo)
        private readonly notificationRepo: NotificationRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(ChatMessageRepo)
        private readonly chatMessageRepo: ChatMessageRepo,
    ) {}

    async createNotification(data: CreateNotificationDto) {
        const notification = this.notificationRepo.create({
            ...data,
            sendDate: new Date(),
        });
        return await this.notificationRepo.save(notification);
    }

    async getNotifications(params: GetNotificationsDto) {
        const { tenantId } = memberSessionContext;
        if (!tenantId) {
            throw new Error('Unauthorized');
        }

        const supplier = await this.supplierRepo.findOne({
            where: { tenantId },
        });

        if (!supplier) {
            throw new Error('Supplier not found');
        }

        const whereCon: any = {
            receiverId: supplier.id,
        };

        if (params.type) {
            whereCon.type = params.type;
        }

        if (params.status) {
            whereCon.status = params.status;
        }

        const notifications = await this.notificationRepo.find({
            where: whereCon,
            order: { createdDate: 'DESC' },
        });

        const notificationsWithDetails = await Promise.all(
            notifications.map(async notification => {
                const senderInfo = await this.supplierRepo.findOne({
                    where: { id: notification.senderId },
                });
                return {
                    ...notification,
                    senderName: senderInfo?.fullName,
                    senderAvatar: senderInfo?.logoUrl,
                };
            }),
        );

        const total = await this.notificationRepo.count({
            where: whereCon,
        });

        return {
            data: notificationsWithDetails,
            total,
        };
    }

    async markAsRead(data: MarkAsReadDto) {
        const { tenantId } = memberSessionContext;
        if (!tenantId) {
            throw new Error('Unauthorized');
        }

        const supplier = await this.supplierRepo.findOne({
            where: { tenantId },
        });

        if (!supplier) {
            throw new Error('Supplier not found');
        }

        const notificationIds = Array.isArray(data.notificationIds)
            ? data.notificationIds
            : [data.notificationIds];

        await this.notificationRepo.update(
            {
                id: In(notificationIds),
                receiverId: supplier.id,
            },
            {
                status: NSNotification.EStatus.READ,
                updatedDate: new Date(),
            },
        );

        return { message: 'Notifications marked as read successfully' };
    }

    async getUnreadCount() {
        const { tenantId } = memberSessionContext;
        if (!tenantId) {
            throw new Error('Unauthorized');
        }

        const supplier = await this.supplierRepo.findOne({
            where: { tenantId },
        });

        if (!supplier) {
            throw new Error('Supplier not found');
        }

        const count = await this.notificationRepo.count({
            where: {
                receiverId: supplier.id,
                status: NSNotification.EStatus.UNREAD,
            },
        });

        return { unreadCount: count };
    }

    // Helper method để tạo thông báo báo giá mới
    async createQuoteNotification(quotationData: any, receiverId: string, senderid?: any) {
        const notification = await this.createNotification({
            receiverId,
            senderId: senderid,
            type: NSNotification.EType.QUOTE,
            title: 'Báo giá mới',
            description: `Bạn có một báo giá mới cho sản phẩm ${quotationData.itemName || ''}`,
            referenceId: quotationData.id,
            referenceType: 'quotation',
            metadata: {
                quotationCode: quotationData.requestCode,
                itemName: quotationData.itemName,
                quantity: quotationData.quantity,
            },
        });

        return notification;
    }

    // Helper method để tạo thông báo tin nhắn mới
    async createMessageNotification(messageData: any, receiverId: string, senderInfo?: any) {
        const message = await this.chatMessageRepo.findOne({
            where: { id: messageData.id },
        });

        const notification = await this.createNotification({
            receiverId,
            senderId: senderInfo?.id,
            type: NSNotification.EType.MESSAGE,
            title: 'Tin nhắn mới',
            description:
                message.messageType === 'text'
                    ? message.text
                    : `${senderInfo?.fullName || 'Người dùng'} đã liên hệ với bạn : ${message.text}`,
            referenceId: messageData.id,
            referenceType: 'message',
            metadata: {
                threadKey: message.threadKey,
                productId: messageData.productId,
                messageType: messageData.messageType,
            },
            senderAvatar: senderInfo?.avatar,
            senderName: senderInfo?.fullName || senderInfo?.name,
        });

        return notification;
    }
}
