import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSNotification } from '~/common/enums/notification.enum';

export class CreateNotificationDto {
    @ApiProperty({ description: 'ID người nhận' })
    @IsUUID()
    receiverId: string;

    @ApiPropertyOptional({ description: 'ID người gửi' })
    @IsOptional()
    @IsUUID()
    senderId?: string;

    @ApiProperty({ description: 'Loại thông báo', enum: NSNotification.EType })
    @IsEnum(NSNotification.EType)
    type: NSNotification.EType;

    @ApiProperty({ description: 'Tiêu đề thông báo' })
    @IsString()
    title: string;

    @ApiPropertyOptional({ description: '<PERSON><PERSON> tả thông báo' })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiPropertyOptional({ description: 'ID tham chiếu' })
    @IsOptional()
    @IsString()
    referenceId?: string;

    @ApiPropertyOptional({ description: 'Loại tham chiếu' })
    @IsOptional()
    @IsString()
    referenceType?: string;

    @ApiPropertyOptional({ description: 'Dữ liệu bổ sung' })
    @IsOptional()
    metadata?: any;

    @ApiPropertyOptional({ description: 'Avatar người gửi' })
    @IsOptional()
    @IsString()
    senderAvatar?: string;

    @ApiPropertyOptional({ description: 'Tên người gửi' })
    @IsOptional()
    @IsString()
    senderName?: string;
}

export class GetNotificationsDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Loại thông báo', enum: NSNotification.EType })
    @IsOptional()
    @IsEnum(NSNotification.EType)
    type?: NSNotification.EType;

    @ApiPropertyOptional({ description: 'Trạng thái', enum: NSNotification.EStatus })
    @IsOptional()
    @IsEnum(NSNotification.EStatus)
    status?: NSNotification.EStatus;
}

export class MarkAsReadDto {
    @ApiProperty({ description: 'ID thông báo hoặc mảng IDs' })
    notificationIds: string | string[];
}
