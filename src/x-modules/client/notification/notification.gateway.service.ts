import { Injectable } from '@nestjs/common';
import {
    WebSocketGateway,
    WebSocketServer,
    OnGatewayConnection,
    OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway({
    namespace: '/notification',
    cors: {
        origin: '*',
    },
})
@Injectable()
export class NotificationGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;

    // Map để track userId -> socketId
    private userSocketMap = new Map<string, string>();
    // Map để track socketId -> userId
    private socketUserMap = new Map<string, string>();

    handleConnection(client: Socket) {
        const userId = client.handshake.query.userId as string;

        if (userId) {
            this.userSocketMap.set(userId, client.id);
            this.socketUserMap.set(client.id, userId);
            console.log(`User ${userId} connected to notifications`);
        }
    }

    handleDisconnect(client: Socket) {
        const userId = this.socketUserMap.get(client.id);

        if (userId) {
            this.userSocketMap.delete(userId);
            this.socketUserMap.delete(client.id);
            console.log(`User ${userId} disconnected from notifications`);
        }
    }

    // Gửi thông báo đến user cụ thể
    sendNotificationToUser(userId: string, notification: any) {
        const socketId = this.userSocketMap.get(userId);

        if (socketId) {
            this.server.to(socketId).emit('newNotification', {
                type: 'notification',
                data: notification,
            });

            // Gửi thêm event để update unread count
            this.server.to(socketId).emit('updateUnreadCount', {
                type: 'unread_count_update',
            });
        }
    }

    // Broadcast thông báo cho tất cả users online
    broadcastNotification(notification: any) {
        this.server.emit('broadcastNotification', {
            type: 'broadcast',
            data: notification,
        });
    }
}
