import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ChatMsgType } from '~/common/enums/message.enum';

export class JoinDto {
    @ApiProperty()
    @IsNotEmpty()
    meId: string;

    @ApiProperty()
    @IsNotEmpty()
    productId: string;

    // otherUserId
    @ApiProperty()
    @IsNotEmpty()
    otherUserId: string;
}

export class SendMessageDto extends JoinDto {
    @ApiProperty({ enum: ChatMsgType })
    @IsIn(Object.values(ChatMsgType))
    messageType: ChatMsgType;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    text?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    fileUrl?: string;
}

export class HistoryDto extends JoinDto {
    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;

    @ApiPropertyOptional()
    @IsOptional()
    beforeId?: string;
}

export class MarkReadDto extends JoinDto {
    @IsOptional() untilId?: string;
}

export class GetConversationMessagesDto {
    @ApiProperty()
    @IsNotEmpty()
    meId: string;

    @ApiProperty()
    @IsNotEmpty()
    productId: string;

    @ApiProperty()
    @IsNotEmpty()
    senderId: string;

    @ApiPropertyOptional()
    @IsOptional()
    limit?: number;

    @ApiPropertyOptional()
    @IsOptional()
    beforeId?: string;
}
