import { Injectable } from '@nestjs/common';
import { ChatService } from './chat.service';
import { HistoryDto } from './dto/chat.dto';
import {
    WebSocketGateway,
    SubscribeMessage,
    MessageBody,
    ConnectedSocket,
    WebSocketServer,
    OnGatewayConnection,
    OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { makeThreadKey } from '~/utils/chat.util';
import { ChatMsgType } from '~/common/enums/message.enum';

@WebSocketGateway({
    namespace: '/chat',
    cors: {
        origin: '*',
    },
})
@Injectable()
export class ChatGatewayService implements OnGatewayConnection, OnGatewayDisconnect {
    constructor(private readonly chatService: ChatService) {}

    @WebSocketServer()
    server: Server;

    // Map để track userId -> socketId
    private userSocketMap = new Map<string, string>();
    // Map để track socketId -> userId (để cleanup)
    private socketUserMap = new Map<string, string>();

    // Khi client kết nối
    handleConnection(client: Socket) {
        const userId = client.handshake.query.userId as string;

        if (userId) {
            // Lưu mapping userId -> socketId
            this.userSocketMap.set(userId, client.id);
            this.socketUserMap.set(client.id, userId);
        }
    }

    // Khi client ngắt kết nối
    handleDisconnect(client: Socket) {
        const userId = this.socketUserMap.get(client.id);

        if (userId) {
            // Xóa mapping
            this.userSocketMap.delete(userId);
            this.socketUserMap.delete(client.id);
        }
    }

    @SubscribeMessage('join')
    async handleJoin(@MessageBody() data: HistoryDto, @ConnectedSocket() client: Socket) {
        const { productId, otherUserId, limit, meId } = data;

        // Update mapping nếu chưa có
        if (!this.userSocketMap.has(meId)) {
            this.userSocketMap.set(meId, client.id);
            this.socketUserMap.set(client.id, meId);
        }

        const threadKey = makeThreadKey(productId, meId, otherUserId);
        client.join(threadKey);

        const history = await this.chatService.history(meId, productId, otherUserId, limit);
        const latestMsg = history && history.length > 0 ? history[0] : null;
        client.emit('history', { history, latestMsg });
        return { history, latestMsg };
    }

    @SubscribeMessage('sendMessage')
    async handleSendMessage(
        @MessageBody()
        data: {
            productId: string;
            otherId: string;
            messageType: ChatMsgType;
            payload: { text?: string; fileUrl?: string };
            meId: string;
        },
        @ConnectedSocket() client: Socket,
    ) {
        const { productId, otherId, messageType, payload, meId } = data;
        const msg = await this.chatService.send(meId, productId, otherId, messageType, payload);

        // Lấy socketId của người nhận
        const receiverSocketId = this.userSocketMap.get(otherId);

        if (receiverSocketId) {
            // Emit trực tiếp đến socketId của người nhận
            this.server.to(receiverSocketId).emit('privateMessage', { data: msg });
        } else {
            client.emit('userOffline', { userId: otherId, message: 'User is currently offline' });
        }

        // Broadcast cho room chung (nếu cần)
        const threadKey = makeThreadKey(productId, meId, otherId);
        this.server.to(threadKey).emit('newMessage', { data: msg });

        // Confirm cho người gửi
        client.emit('sendDataServer', { data: msg });
        return msg;
    }

    @SubscribeMessage('listMessageItem')
    async handleListMessageItem(
        @MessageBody() data: { meId: string; limit: number },
        @ConnectedSocket() client: Socket,
    ) {
        const { meId, limit } = data;
        const list = await this.chatService.listMessageItem(meId, limit);
        client.emit('listHistoryMessage', { list });
        return list;
    }

    @SubscribeMessage('getConversationMessages')
    async handleGetConversationMessages(
        @MessageBody()
        data: {
            meId: string;
            productId: string;
            senderId: string;
            limit?: number;
            beforeId?: string;
        },
        @ConnectedSocket() client: Socket,
    ) {
        const { meId, productId, senderId, limit = 30, beforeId } = data;
        const messages = await this.chatService.getConversationMessages(
            meId,
            productId,
            senderId,
            limit,
            beforeId,
        );
        client.emit('conversationMessages', { messages, productId, senderId });
        return messages;
    }
}
