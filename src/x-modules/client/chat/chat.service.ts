// src/chat/chat.service.ts
import { Injectable } from '@nestjs/common';
import { In, LessThan } from 'typeorm';
import { ChatMsgType } from '~/common/enums/message.enum';
import { ChatMessageEntity } from '~/domains/primary/chat/chat-message.entity';
import { makeThreadKey } from '~/utils/chat.util';
import { ChatMessageRepo } from '~/domains/primary/chat/chat-message.repo';
import { SupplierRepo } from '~/domains/primary/supplier/supplier.repo';
import { ItemRepo } from '~/domains/primary/item/item.repo';
import { NotificationService } from '../notification/notification.service';
import { NotificationGateway } from '../notification/notification.gateway.service';

@Injectable()
export class ChatService {
    constructor(
        private readonly repo: ChatMessageRepo,
        private readonly supplierRepo: SupplierRepo,
        private readonly itemRepo: ItemRepo,
        private readonly notificationService: NotificationService,
        private readonly notificationGateway: NotificationGateway,
    ) {}

    async listMessageItem(meId: string, limit = 30) {
        // L<PERSON>y tất cả message có receiverId là meId
        const messages = await this.repo.find({
            where: { receiverId: meId },
            order: { createdAt: 'DESC' },
        });

        // Duyệt qua messages để lấy duy nhất mỗi cặp senderId/productId
        const uniqueMap = new Map<
            string,
            { senderId: string; productId: string; createdAt: Date }
        >();
        for (const msg of messages) {
            const key = `${msg.productId}_${msg.senderId}`;
            if (!uniqueMap.has(key)) {
                uniqueMap.set(key, {
                    senderId: msg.senderId,
                    productId: msg.productId,
                    createdAt: msg.createdAt,
                });
            }
        }

        // Lấy ra mảng kết quả, giới hạn theo limit
        const result = Array.from(uniqueMap.values())
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(0, limit);

        // lấy danh sách supplier theo senderId
        const supplier = await this.supplierRepo.find({
            where: {
                id: In(result.map(item => item.senderId)),
            },
        });

        const listSupplier = result.map(item => {
            const supplierItem = supplier.find(supplier => supplier.id === item.senderId);
            return {
                ...item,
                sender: {
                    id: supplierItem.id,
                    fullName: supplierItem.fullName,
                    logoUrl: supplierItem.logoUrl,
                    phone: supplierItem.phone,
                    email: supplierItem.email,
                    address: supplierItem.address,
                },
            };
        });

        // lấy thông tin tri tiết của item trong listSupplier
        const items = await this.itemRepo.find({
            where: {
                id: In(listSupplier.map(item => item.productId)),
            },
        });

        const listItem = listSupplier.map(i => {
            const itemItem = items.find(item => item.id === i.productId);
            return {
                ...i,
                item: {
                    id: itemItem.id,
                    name: itemItem.name,
                    primaryImg: itemItem.primaryImg,
                },
            };
        });

        return listItem;
    }

    async history(meId: string, productId: string, otherId: string, limit = 30) {
        const list = await this.repo.find({
            where: [
                { senderId: otherId, receiverId: meId, productId },
                { senderId: meId, receiverId: otherId, productId },
            ],
            order: { createdAt: 'ASC' },
            take: limit,
        });

        return list;
    }

    async send(
        meId: string,
        productId: string,
        otherId: string,
        messageType: ChatMsgType,
        payload: { text?: string; fileUrl?: string; metadata?: any },
    ) {
        const threadKey = makeThreadKey(productId, meId, otherId);
        const entity = this.repo.create({
            threadKey,
            productId,
            senderId: meId,
            receiverId: otherId,
            createdAt: new Date(),
            messageType,
            text: payload.text,
            fileUrl: payload.fileUrl,
        });
        const message = await this.repo.save(entity);

        // Tạo thông báo cho người nhận tin nhắn
        try {
            // Lấy thông tin người gửi
            const senderSupplier = await this.supplierRepo.findOne({
                where: { id: meId },
            });

            const notification = await this.notificationService.createMessageNotification(
                message,
                otherId,
                senderSupplier,
            );

            // Gửi thông báo real-time qua socket
            this.notificationGateway.sendNotificationToUser(otherId, notification);
        } catch (error) {
            console.error('Error creating message notification:', error);
        }

        return message;
    }

    async markRead(meId: string, productId: string, otherId: string, untilId?: string) {
        const threadKey = makeThreadKey(productId, meId, otherId);

        if (!untilId) {
            await this.repo
                .createQueryBuilder()
                .update(ChatMessageEntity)
                .set({ readAt: () => 'NOW()' })
                .where(
                    'threadKey = :threadKey AND receiverId = :meId AND readAt IS NULL AND isDeleted = false',
                    { threadKey, meId },
                )
                .execute();
            return;
        }

        const until = await this.repo.findOne({ where: { id: untilId, threadKey } });
        if (!until) return;

        await this.repo
            .createQueryBuilder()
            .update(ChatMessageEntity)
            .set({ readAt: () => 'NOW()' })
            .where(
                'threadKey = :threadKey AND receiverId = :meId AND readAt IS NULL AND isDeleted = false AND createdAt <= :ts',
                { threadKey, meId, ts: until.createdAt },
            )
            .execute();
    }

    async countUnread(threadKey: string, userId: string) {
        const { count } = await this.repo
            .createQueryBuilder('m')
            .select('COUNT(*)', 'count')
            .where(
                'm.threadKey = :threadKey AND m.receiverId = :uid AND m.readAt IS NULL AND m.isDeleted = false',
                { threadKey, uid: userId },
            )
            .getRawOne();
        return Number(count || 0);
    }

    async unreadCountByThread(meId: string) {
        return this.repo.query(
            `SELECT thread_key AS "threadKey", COUNT(*)::int AS "unread"
       FROM chat_message
       WHERE receiver_id = $1 AND read_at IS NULL AND is_deleted = false
       GROUP BY thread_key`,
            [meId],
        );
    }

    async getConversationMessages(
        meId: string,
        productId: string,
        senderId: string,
        limit = 30,
        beforeId?: string,
    ) {
        const threadKey = makeThreadKey(productId, meId, senderId);

        let whereCondition: any = {
            threadKey,
            isDeleted: false,
        };

        // Nếu có beforeId, lấy tin nhắn trước ID đó để hỗ trợ phân trang
        if (beforeId) {
            const beforeMessage = await this.repo.findOne({ where: { id: beforeId } });
            if (beforeMessage) {
                whereCondition.createdAt = LessThan(beforeMessage.createdAt);
            }
        }

        const messages = await this.repo.find({
            where: whereCondition,
            order: { createdAt: 'DESC' },
            take: limit,
        });

        // Lấy thông tin chi tiết của sender và receiver
        const userIds = [
            ...new Set([...messages.map(m => m.senderId), ...messages.map(m => m.receiverId)]),
        ];
        const suppliers = await this.supplierRepo.find({
            where: { id: In(userIds) },
        });

        // Kết hợp thông tin
        const messagesWithDetails = messages.map(message => {
            const sender = suppliers.find(s => s.id === message.senderId);
            const receiver = suppliers.find(s => s.id === message.receiverId);

            return {
                ...message,
                sender: sender
                    ? {
                          id: sender.id,
                          fullName: sender.fullName,
                          logoUrl: sender.logoUrl,
                      }
                    : null,
                receiver: receiver
                    ? {
                          id: receiver.id,
                          fullName: receiver.fullName,
                          logoUrl: receiver.logoUrl,
                      }
                    : null,
            };
        });

        // Sắp xếp lại theo thứ tự tăng dần (tin nhắn cũ lên trên)
        return messagesWithDetails.reverse();
    }
}
