import { ApiProperty } from '@nestjs/swagger';
import { PageRequest } from '~/@systems/utils';

export class ItemDto extends PageRequest {
    @ApiProperty({ description: 'ID nhà cung cấp', required: false })
    supplierId: string;

    @ApiProperty({ description: 'Mã sản phẩm', required: false })
    code: string;

    @ApiProperty({ description: 'Tên sản phẩm', required: false })
    name: string;

    @ApiProperty({ description: 'Trạng thái sản phẩm', required: false })
    isActive: boolean;

    @ApiProperty({ description: 'Danh mục sản phẩm', required: false })
    categoryId: string;
}

export class RelatedItemDto {
    @ApiProperty({ description: 'ID sản phẩm', required: false })
    itemId: string;

    @ApiProperty({ description: 'ID danh mục sản phẩm', required: false })
    categoryId: string;
}

export class SupplierByCategoryDto {
    @ApiProperty({ description: 'ID sản phẩm', required: false })
    categoryId: string;
}

export class CreateItemDto {
    @ApiProperty({ description: 'Id sản phẩm', required: false })
    id: string;
    
    @ApiProperty({ description: 'Tên sản phẩm', required: true })
    name: string;

    @ApiProperty({ description: 'Mã sản phẩm', required: true })
    code: string;

    @ApiProperty({ description: 'Mô tả ngắn', required: false })
    shortDesc: string;

    @ApiProperty({ description: 'Giá', required: false })
    price: any[];

    @ApiProperty({ description: 'Đơn vị của sản phẩm', required: false })
    unit: string;

    @ApiProperty({ description: 'Danh mục sản phẩm', required: false })
    categoryId: any[];

    @ApiProperty({
        description: 'Ảnh chinh sản phẩm',
        required: false,
        example: {
            uid: '1',
            name: 'anh1.png',
            url: 'https://example.com/bao-gia.pdf',
        },
    })
    primaryImg: any[];

    @ApiProperty({
        description: 'Hình ảnh khác của sản phẩm',
        required: false,
        type: 'array',
        items: {
            type: 'object',
            properties: {
                uid: { type: 'string', example: '1' },
                name: { type: 'string', example: 'anh1.png' },
                url: { type: 'string', example: 'https://example.com/bao-gia.pdf' },
            },
        },
    })
    images: any[];

    @ApiProperty({
        description: 'File tài liệu của sản phẩm',
        required: false,
        type: 'array',
        items: {
            type: 'object',
            properties: {
                uid: { type: 'string', example: '1' },
                name: { type: 'string', example: 'anh1.png' },
                url: { type: 'string', example: 'https://example.com/bao-gia.pdf' },
            },
        },
    })
    urlFile: any[];

    // technicalSpecs
    @ApiProperty({
        description: 'Thông số kỹ thuật của sản phẩm',
        example: [{ key: 'key', value: 'value' }],
        required: false,
    })
    technicalSpecs: any[];
}

export class UpdateItemDto extends CreateItemDto {
    @ApiProperty({ description: 'Id sản phẩm', required: true })
    id: string;
}

export class UpdateItemStatusDto {
    @ApiProperty({ description: 'Id sản phẩm', required: true })
    id: string;
    @ApiProperty({ description: 'Trạng thái sản phẩm', required: true })
    isActive: boolean;
}
