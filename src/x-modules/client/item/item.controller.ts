import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { ItemService } from './item.service';
import { Body, Query } from '@nestjs/common';
import { CreateItemDto, UpdateItemDto, UpdateItemStatusDto } from './dto/item.dto';
import { ItemDto, ItemReq } from '~/x-modules/publics/item/dto/item.dto';

@DefController('item')
export class ItemController {
    constructor(private readonly itemService: ItemService) {}

    @DefGet('list')
    async getAll(@Query() data: ItemReq) {
        return await this.itemService.list(data);
    }

    @DefPost('detail')
        async getDetail(@Body() data: ItemDto) {
        return await this.itemService.detail(data);
    }

     @DefPost('insert')
     async insert(@Body() data: CreateItemDto) {
        return await this.itemService.insert(data);
    }

    @DefPost('update')
    async update(@Body() data: UpdateItemDto) {
        return await this.itemService.update(data);
    }

    @DefPost('update-status')
    async updateStatus(@Body() data: UpdateItemStatusDto) {
        return await this.itemService.updateStatus(data);
    }
    

}
