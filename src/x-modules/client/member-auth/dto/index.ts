import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { isNotEmpty, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import * as jwt from 'jsonwebtoken';
import { NSMember } from '~/common/enums/member.enum';

export class LoginReq {
    @ApiProperty()
    @IsNotEmpty()
    username: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;
}

export class JwtPayload implements jwt.JwtPayload {
    @ApiPropertyOptional()
    iss?: string | undefined;
    @ApiPropertyOptional()
    sub?: string | undefined;
    @ApiPropertyOptional()
    aud?: string | string[] | undefined;
    @ApiPropertyOptional()
    exp?: number | undefined;
    @ApiPropertyOptional()
    nbf?: number | undefined;
    @ApiPropertyOptional()
    iat?: number | undefined;
    @ApiPropertyOptional()
    jti?: string | undefined;
}

export class MemberSessionPayload extends JwtPayload {
    @ApiProperty()
    id: string;

    @ApiProperty()
    tenantId: string;

    @ApiProperty()
    username: string;

    @ApiProperty({ enum: NSMember.EStatus })
    status: NSMember.EStatus;

    /** Email */
    @ApiPropertyOptional()
    email?: string;

    @ApiPropertyOptional()
    fullName?: string;

    @ApiPropertyOptional()
    address?: string;
}

export class MemberSessionDto extends MemberSessionPayload {
    @ApiProperty()
    accessToken: string;
    @ApiProperty()
    refreshToken: string;
    @ApiProperty()
    tokenType: 'Bearer' = 'Bearer';
}

// reset pasword
export class ResetPasswordDto {
    @ApiProperty()
    @IsNotEmpty()
    accountId: string;

    @ApiProperty()
    @IsNotEmpty()
    oldPassword: string;

    @ApiProperty()
    @IsNotEmpty()
    newPassword: string;

    @ApiProperty()
    @IsNotEmpty()
    confirmPassword: string;
}
