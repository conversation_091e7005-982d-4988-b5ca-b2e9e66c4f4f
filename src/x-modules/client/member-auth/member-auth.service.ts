import { BusinessException } from '~/@systems/exceptions';
import { JwtService } from '@nestjs/jwt';
import { configEnv } from '~/@config/env';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { NSMember } from '~/common/enums/member.enum';
import { Injectable } from '@nestjs/common';
import { SupplierRepo } from '~/domains/primary';
import { NSSupplier } from '~/common/enums/supplier.enum';
@Injectable()
export class MemberAuthService {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(SupplierRepo) private supplierRepo: SupplierRepo,
    ) {}

    private async generateRefreshToken(memberId: string) {
        const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
        const newRefreshToken = await this.jwtService.signAsync(
            { sub: memberId },
            {
                secret: JWT_REFRESH_TOKEN_SECRET,
                expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
            },
        );

        return newRefreshToken;
    }

    private clearPrivateMemberData(member: any) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = member;
        return rest;
    }

    private async makeAuthResponse(member: any) {
        const pipeMember = this.clearPrivateMemberData(member);
        const payload = {
            sub: member.id,
            ...pipeMember,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.generateRefreshToken(member.id),
            tokenType: 'Bearer',
            ...pipeMember,
        };
    }

    async apeLogin(profile: {
        id: string;
        providerId: string;
        username: string;
        fullName: string;
        phone: string;
        email: string;
        tenantInfo: {
            id: string;
            name: string;
            domain: string;
            logoUrl: string;
            website: string;
            address: string;
            taxCode: string;
            phone: string;
            email: string;
        };
    }) {
        const { tenantInfo } = profile;
        let member = await this.supplierRepo.findOne({
            where: {
                tenantId: tenantInfo.id,
            },
        });

        if (!member) {
            member = await this.supplierRepo.save({
                tenantId: tenantInfo.id,
                username: profile.tenantInfo.name,
                fullName: profile.fullName,
                phone: profile.tenantInfo.phone,
                email: profile.tenantInfo.email,
                domain: profile.tenantInfo.domain,
                logoUrl: profile.tenantInfo.logoUrl,
                websiteUrl: profile.tenantInfo.website,
                address: profile.tenantInfo.address,
                taxCode: profile.tenantInfo.taxCode,
                status: NSSupplier.EStatus.ACTIVE,
                isAuth: true,
                accountId: profile.providerId,
            });

            return this.makeAuthResponse(member);
        }
        if (member.status === NSMember.EStatus.LOCKED) {
            throw new BusinessException('member_auth.login.error.locked');
        }
        return this.makeAuthResponse(member);
    }
}
