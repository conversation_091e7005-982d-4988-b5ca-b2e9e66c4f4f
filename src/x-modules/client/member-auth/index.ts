import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { lazyLoadClasses } from '~/utils/tech.util';
import { RefixModule } from '../../config-module';
const controllers = lazyLoadClasses(__dirname, ['.controller']);
const services = lazyLoadClasses(__dirname, ['.service']);

@ChildModule({
    prefix: RefixModule.supplier,
    imports: [PrimaryRepoModule],
    providers: [...services],
    controllers: [...controllers],
})
export class ClientModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
