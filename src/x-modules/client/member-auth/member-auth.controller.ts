import { MemberAuthService } from './member-auth.service';
import { Body, Req, Res } from '@nestjs/common';
import { UseGuards } from '@nestjs/common';
import * as express from 'express';
import * as querystring from 'querystring';
import { DefController, DefGet, DefPost, InjectRepo } from 'nestjs-typeorm3-kit';
import { ApeSsoAuthGuard } from '~/x-modules/@guards/ape-sso-auth/ape-sso-auth.guard';
import { optionalApiConnector } from '~/connectors';
import { configEnv } from '~/@config/env';
import { ClientAuthGuard } from '~/x-modules/client/@guards/client-auth/client-auth.guard';
import { LoginReq, ResetPasswordDto } from '~/x-modules/client/member-auth/dto';
import { memberSessionContext } from '../member-session.context';
import { SupplierRepo } from '~/domains/primary';

const { APE_SSO_URL, APE_CLIENT_ID, APE_CLIENT_SECRET, APE_CALLBACK_URL } = configEnv();

@DefController('auth')
export class MemberAuthController {
    constructor(
        private readonly memberAuthService: MemberAuthService,
        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,
    ) {}

    @DefPost('login')
    async login(@Body() body: LoginReq) {
        const loginEndpoint = `${APE_SSO_URL}/api/client/auth/login`;
        const userInfoEndpoint = `${APE_SSO_URL}/api/client/auth/userinfo`;
        const loginResult = await optionalApiConnector.post<{ access_token: string }>(
            loginEndpoint,
            {
                ...body,
                clientId: APE_CLIENT_ID,
                clientSecret: APE_CLIENT_SECRET,
                redirectUri: APE_CALLBACK_URL,
            },
        );
        const accessToken = loginResult.access_token;
        const userInfoResult = await optionalApiConnector.get(
            userInfoEndpoint,
            {},
            {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            },
        );
        return this.memberAuthService.apeLogin(userInfoResult);
    }

    @UseGuards(ClientAuthGuard)
    @DefGet('me', {
        summary: 'Get profile user',
    })
    me(@Req() req: express.Request & { user: any }) {
        return req.user;
    }

    @UseGuards(ApeSsoAuthGuard)
    @DefGet('ape')
    apeLogin(@Req() req: express.Request, @Res() res: express.Response) {}

    @DefGet('ape/callback')
    @UseGuards(ApeSsoAuthGuard)
    async apeAuthRedirect(@Req() req, @Res() res: express.Response) {
        const result = await this.memberAuthService.apeLogin(req.user);
        const redirectUri = req.user.redirectUri;
        const query = querystring.stringify(result);
        const finalRedirect = `${redirectUri}?${query}`;
        return res.redirect(finalRedirect);
    }

     @DefPost('reset-password')
    async resetPassword(@Body() body: ResetPasswordDto) {
        const resetPasswordEndpoint = `${APE_SSO_URL}/api/public/account/sync-update-password`;
         const headers = {
            'Content-Type': 'application/json',
            'x-api-key': process.env.APE_API_KEY,    
        }; 

        const data = {
            accountId: body.accountId,
            oldPassword: body.oldPassword,
            newPassword: body.newPassword,
            confirmPassword: body.confirmPassword,
        };

        const res = await optionalApiConnector.post(resetPasswordEndpoint, data, { headers });
        return res
     
    }
}
