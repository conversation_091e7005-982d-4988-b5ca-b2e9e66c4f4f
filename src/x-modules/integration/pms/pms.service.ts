import { SupplierService } from './../../client/supplier/supplier.service';
import { Injectable, Logger } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { configEnv } from '~/@config/env';
import { optionalApiConnector } from '~/connectors';
import { SupplierRepo } from '~/domains/primary';
import { SupplierLawRepo } from '~/domains/primary/supplier-law/supplier-law.repo';
import { SupplierOpacityRepo } from '~/domains/primary/supplier-opacity/supplier-opacity.repo';
const { APE_SSO_URL, APE_CLIENT_ID } = configEnv();

@Injectable()
export class PmsService {
    private readonly logger = new Logger(PmsService.name);

    constructor(
        private readonly supplierRepo: SupplierRepo,
        private readonly supplierLawRepo: SupplierLawRepo,
        private readonly supplierOpacityRepo: SupplierOpacityRepo,
    ) {}

    /**
     * N<PERSON>ận mảng SupplierOutboundPayload, map sang các bảng và upsert.
     * Tr<PERSON> về summary: processed, created, updated, errors[]
     */
    async receiveSuppliers(payloads: any[]) {
        const summary = { processed: 0, created: 0, updated: 0, errors: [] as any[] };

        for (const p of payloads) {
            summary.processed++;
            try {
                // tìm supplier theo id nếu có
                let supplier = null;
                if (p.id) {
                    supplier = await this.supplierRepo.findOne({ where: { id: p.id } });
                }

                // chuẩn bị data cho bảng supplier (map từ  PMS DTO)
                const dataSup: DeepPartial<any> = {
                    fullName: p.name ?? undefined,
                    email: p.email ?? undefined,
                    phone: p.phone ?? undefined,
                    address: p.address ?? undefined,
                    taxCode: p.code ?? undefined, // map code -> taxCode
                    logoUrl: undefined,
                    websiteUrl: undefined,
                    // giữ nguyên id khi tạo mới nếu payload cung cấp
                    ...(p.id ? { id: p.id } : {}),
                };

                if (supplier) {
                    await this.supplierRepo.update(supplier.id, dataSup);
                    this.logger.log(`Cập nhật supplier id=${supplier.id} từ  PMS`);
                    summary.updated++;
                } else {
                    const created = await this.supplierRepo.save(dataSup);
                    supplier = created;
                    this.logger.log(`Tạo mới supplier id=${created.id} từ  PMS`);
                    summary.created++;
                }

                // Upsert SupplierLaw: map các trường phù hợp từ  PMS
                const supplierLaw = await this.supplierLawRepo.findOne({
                    where: { supplierId: supplier.id },
                });
                const dataLaw: DeepPartial<any> = {
                    companyTypes: undefined,
                    profileTypes: undefined,
                    dateOfIssue: undefined,
                    placeOfIssue: undefined,
                    profileName: p.dealName ?? undefined,
                    addressOfIssue: undefined,
                    dealAddress: p.dealAddress ?? undefined,
                    capital:
                        typeof p.capital === 'number'
                            ? p.capital
                            : p.capital
                              ? Number(p.capital)
                              : undefined,
                    fax: undefined,
                    supplierId: supplier.id,
                    dateOfEstablishment: p.createYear ? String(p.createYear) : undefined,
                    legalRepName: p.represen ?? p.contactName ?? undefined,
                    legalRepPosition: p.chief ?? undefined,
                    legalRepPhone: p.phone ?? undefined,
                    legalRepEmail: p.email ?? undefined,
                };

                if (supplierLaw) {
                    await this.supplierLawRepo.update(supplierLaw.id, dataLaw);
                    this.logger.log(`Cập nhật supplierLaw for supplierId=${supplier.id}`);
                } else {
                    await this.supplierLawRepo.save(dataLaw);
                    this.logger.log(`Tạo supplierLaw cho supplierId=${supplier.id}`);
                }

                // Upsert SupplierOpacity: map các trường mô tả / assets
                const supplierOpacity = await this.supplierOpacityRepo.findOne({
                    where: { supplierId: supplier.id },
                });
                const dataOpacity: DeepPartial<any> = {
                    businessAreas: undefined,
                    certificatesFiles: undefined,
                    recentRevenue: undefined,
                    financialReports: undefined,
                    referenceDocuments: undefined,
                    supplierId: supplier.id,
                };

                if (p.description) {
                    (dataOpacity as any).description = p.description;
                }
                if (p.assets !== undefined) {
                    (dataOpacity as any).assets =
                        typeof p.assets === 'number' ? p.assets : Number(p.assets);
                }

                if (supplierOpacity) {
                    await this.supplierOpacityRepo.update(supplierOpacity.id, dataOpacity);
                    this.logger.log(`Cập nhật supplierOpacity for supplierId=${supplier.id}`);
                } else {
                    await this.supplierOpacityRepo.save(dataOpacity);
                    this.logger.log(`Tạo supplierOpacity cho supplierId=${supplier.id}`);
                }
            } catch (err) {
                this.logger.error(
                    `Lỗi xử lý outbound supplier id=${p.id ?? 'N/A'}: ${(err as any)?.message ?? err}`,
                );
                summary.errors.push({ id: p.id, error: (err as any)?.message ?? err });
            }
        }

        this.logger.log(
            `Hoàn tất xử lý ${summary.processed} supplier(s) từ outbound. Tạo=${summary.created}, Cập nhật=${summary.updated}, Lỗi=${summary.errors.length}`,
        );
        return summary;
    }

    async syncDataSupplierToAuth() {
        try {
            const endpoint = `${APE_SSO_URL}/api/public/account/sync-suppliers`;
            const suppliers = await this.supplierRepo.find({
                where: { isAuth: false },
            });

            const headers = {
                'Content-Type': 'application/json',
                'x-api-key': process.env.APE_API_KEY,    
            };
            const suppliersMap = suppliers.map((item) => {
                return {
                    id: item.id,
                    fullName: item.fullName,
                    name: item.fullName,
                    email: item.email,
                    phone: item.phone,
                    address: item.address,
                    code: item.taxCode,
                    website: item.websiteUrl,
                    type: "TENANT_MASTER",
                    tax: item.taxCode,
                };
            });
                
            const data = {
                clientId: APE_CLIENT_ID,
                data: suppliersMap,
            }

            const res = await optionalApiConnector.post(endpoint, data, { headers });
            this.logger.log(`Sync ${suppliers.length} suppliers to auth`);
            if (res) {
                this.logger.log(res.message);
            }
            this.logger.log(res);
            if(res.data && res.data.length > 0) {
                for (const supplier of res.data) {
                    await this.supplierRepo.update({taxCode: supplier.taxCode}, { isAuth: true });
                }
            }
        } catch (error) {
            this.logger.error(error);
        }
    }

}
