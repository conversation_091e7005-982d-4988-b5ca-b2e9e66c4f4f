import { DefController,  DefPost } from 'nestjs-typeorm3-kit';
import { Body } from '@nestjs/common';
import { PmsService } from './pms.service';

@DefController('pms')
export class PmsController {
    constructor(private readonly pmsService: PmsService) {}
    @DefPost('sync-data', {
        summary: 'Hàm sync data supplier pms',
    })
    async syncDataPms(@Body() data: any) {
        // const items = Array.isArray(data) ? data : [data];
        // console.log(`Nhận ${items.length} supplier(s) từ outbound`);
        // const res = await this.pmsService.receiveSuppliers(items);
        // return res;
    }

    @DefPost('sync-data-to-auth', {
        summary: 'Hàm sync data supplier pms',
    })
    async syncDataSupplierToAuth() {
        return await this.pmsService.syncDataSupplierToAuth();
    }
}
