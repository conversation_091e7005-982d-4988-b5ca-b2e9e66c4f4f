import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { configEnv } from '~/@config/env';

const { REDIS_URL } = configEnv();

export class RedisIoAdapter extends IoAdapter {
    private adapterConstructor: ReturnType<typeof createAdapter>;

    async connectToRedis(): Promise<void> {
        // Nếu không có REDIS_URL, bỏ qua kết nối Redis
        if (!REDIS_URL) {
            console.log('⚠️ REDIS_URL not configured, skipping Redis connection');
            return;
        }

        try {
            const pubClient = createClient({
                url: REDIS_URL,
                // socket: {
                //   tls: true,
                // },
            });
            const subClient = pubClient.duplicate();

            await pubClient.connect();
            await subClient.connect();
            console.log('✅ Redis connected');
            this.adapterConstructor = createAdapter(pubClient, subClient);
        } catch (error) {
            console.error('❌ Redis connection failed:', error.message);
            console.log('⚠️ Continuing without Redis adapter');
        }
    }

    createIOServer(port: number, options?: any): any {
        const server = super.createIOServer(port, options);
        // Chỉ sử dụng Redis adapter nếu đã kết nối thành công
        if (this.adapterConstructor) {
            server.adapter(this.adapterConstructor);
        }
        return server;
    }
}
