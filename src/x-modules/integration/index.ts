import { ChildModule } from 'nestjs-typeorm3-kit';
import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { lazyLoadClasses } from '~/utils/tech.util';
import { join } from 'path';
import { RefixModule } from '~/x-modules/config-module';
const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);
@ChildModule({
    prefix: RefixModule.integration,
    imports: [PrimaryRepoModule],
    providers: [...services],
    controllers: [...controllers],
})
export class IntegrationModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
