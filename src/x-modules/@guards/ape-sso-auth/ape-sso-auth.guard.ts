import { AuthGuard } from '@nestjs/passport';
import { ExecutionContext, Injectable } from '@nestjs/common';

export const GUARD_CODE = 'ape-sso';

@Injectable()
export class ApeSsoAuthGuard extends AuthGuard(GUARD_CODE) {
    canActivate(context: ExecutionContext) {
        const req = context.switchToHttp().getRequest();
        console.log(`=====APE AUTH GUARD=====`, req.query);
        const redirectUri = req.query.redirectUri;
        if (redirectUri) {
            req.session = req.session || {};
            req.session.redirectUri = redirectUri;
        }

        return super.canActivate(context);
    }

    getAuthenticateOptions(context: ExecutionContext) {
        const req = context.switchToHttp().getRequest();
        return {
            state: req.session?.redirectUri || '',
        };
    }
}
