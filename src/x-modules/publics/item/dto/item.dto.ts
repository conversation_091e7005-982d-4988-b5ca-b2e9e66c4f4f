import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class CreateItemDto {
    @ApiProperty({ description: 'Tên sản phẩm', required: true })
    name: string;

    @ApiProperty({ description: 'Mã sản phẩm', required: true })
    code: string;

    @ApiProperty({ description: 'Mô tả ngắn', required: false })
    shortDesc: string;

    @ApiProperty({ description: 'Gi<PERSON> ', required: false })
    price: string[];

    @ApiProperty({ description: 'Đơn vị của sản phẩm', required: false })
    unit: string;

    @ApiPropertyOptional({
        description: 'Danh sách ID danh mục sản phẩm',
        example: ['1', '2', '3'],
    })
    categoryIds: any[];

    @ApiProperty({ description: 'Ảnh chinh sản phẩm', required: false })
    primaryImage: any[];

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> ảnh khác của sản phẩm', required: false })
    images: any[];

    @ApiProperty({ description: 'File tài liệu của sản phẩm', required: false })
    fileUrl: string;

    // technicalSpecs
    @ApiProperty({
        description: 'Thông số kỹ thuật của sản phẩm',
        example: [{ key: 'key', value: 'value' }],
        required: false,
    })
    technicalSpecs: any[];
}

export class ItemReq extends PageRequest {
    @ApiProperty({ description: 'The name of the item', required: false })
    name: string;

    @ApiProperty({ description: 'searchType', required: false })
    searchType: string;

    @ApiProperty({ description: 'searchText', required: false })
    searchText: string;

    @ApiProperty({ description: 'The name supplier of the item', required: false })
    supplierName: string;

    @ApiProperty({ description: 'The code of the item', required: false })
    code: string;

    @ApiPropertyOptional({
        description: 'Danh sách ID danh mục sản phẩm',
        example: ['1', '2', '3'],
    })
    categoryIds: any[];

    @ApiPropertyOptional({
        description: 'Danh sách ID của nhà cung cấpa',
        example: ['1', '2', '3'],
    })
    @IsOptional()
    @ApiProperty({ description: 'The supplier ids of the item', required: false })
    supplierIds: any[];

    @ApiProperty({ description: 'Giá từ', required: false })
    priceFrom: number;

    @ApiProperty({ description: 'Giá đến', required: false })
    priceTo: number;

    @ApiProperty({ description: 'Trạng thái sản phẩm', required: false, default: null })
    isActive: boolean;
}

export class ItemDto {
    @ApiProperty({ description: 'Id sản phẩm', required: true })
    id: string;

}

export class RelatedItemDto {
    @ApiProperty({ description: 'ID sản phẩm', required: false })
    itemId: string;
}

export class SupplierByCategoryDto {
    @ApiProperty({ description: 'ID sản phẩm', required: false })
    itemId: string;

    @ApiProperty({ description: 'ID nhà cung cấp', required: false })
    supplierId: string;
}
        