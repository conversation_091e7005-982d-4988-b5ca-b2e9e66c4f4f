import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ItemRepo, ServiceRepo, SupplierRepo } from '~/domains/primary';
import {
    ItemDto,
    ItemReq,
    RelatedItemDto,
    SupplierByCategoryDto,
} from './dto/item.dto';
import { ILike, In, LessThanOrEqual, Like, MoreThanOrEqual, Not, Raw } from 'typeorm';
import { CommentRepo } from '~/domains/primary/comment/comment.repo';
import { ItemPriceRepo } from '~/domains/primary/item-price/item-price.repo';
@Injectable()
export class ItemService {
    constructor(
        @InjectRepo(ItemRepo)
        private readonly itemRepo: ItemRepo,

        @InjectRepo(CommentRepo)
        private readonly commentRepo: CommentRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(ServiceRepo)
        private readonly serviceRepo: ServiceRepo,

        @InjectRepo(ItemPriceRepo)
        private readonly itemPriceRepo: ItemPriceRepo,
    ) {}
    async list(params: ItemReq) {
        const whereCon: any = {};
        if (params.code?.trim()) {
            whereCon.code = Like(`%${params.code.trim()}%`);
        }
        if (params.name?.trim()) {
            whereCon.name = ILike(`%${params.name.trim()}%`);
        }

        if (params.searchText?.trim()) {
            if (params.searchType === 'product') {
                whereCon.name = ILike(`%${params.searchText.trim()}%`);
            } else if (params.searchType === 'supplier') {
                const suppliers = await this.supplierRepo.find({
                    where: {
                        fullName: ILike(`%${params.searchText.trim()}%`),
                    },
                });
                const supplierIds = suppliers.map(item => item.id);
                whereCon.supplierId = In(supplierIds);
            }
        }
   
        if (params.categoryIds) {
            const categoryIds = Array.isArray(params.categoryIds)
                ? params.categoryIds.map(s => String(s).trim()) 
                : String(params.categoryIds).split(',').map(s => s.trim());

            whereCon.categoryId = Raw(
                (alias) => `${alias} ?| ARRAY[:...categoryIds]::text[]`,
                { categoryIds },
            );
        }
        if (params.priceFrom !== undefined || params.priceTo !== undefined) {
            const values: any[] = [];
            const having: string[] = ['TRUE']; 

            if (params.priceFrom !== undefined) {
                values.push(params.priceFrom);
                having.push(`MIN(ip."price") >= $${values.length}`);
            }
            if (params.priceTo !== undefined) {
                values.push(params.priceTo);
                having.push(`MAX(ip."price") <= $${values.length}`);
            }

            const sql = `
                SELECT ip."itemId" AS "itemId",
                    MIN(ip."price") AS "minPrice",
                    MAX(ip."price") AS "maxPrice"
                FROM "item-price" ip
                GROUP BY ip."itemId"
                HAVING ${having.join(' AND ')}
            `;

            const rows = await this.itemPriceRepo.query(sql, values); // ✅ mảng
            const itemIds = rows.map((row: any) => row.itemId);
            whereCon.id = In(itemIds);
        }

        
        const res = await this.itemRepo.findPagination(
            {
            where: { ...whereCon, isActive: true },
            order: { id: 'DESC' },
            },
            params,
        );

        const supplierIds = res.data.map(item => item.supplierId);
        const suppliers = await this.supplierRepo.findBy({
            id: In(supplierIds),
        });
        const itemIds = res.data.map(item => item.id);
        const prices = await this.itemPriceRepo.find({
            where: { itemId: In(itemIds) },
        });
        res.data = res.data.map(item => {
            const supplier = suppliers.find(supplier => supplier.id === item.supplierId);
            const itemPrices = prices.filter(p => p.itemId === item.id);
            let minPrice: number | null = null;
            let maxPrice: number | null = null;
            if (itemPrices.length > 0) {
                const values = itemPrices.map(p => p.price); 
                minPrice = Math.min(...values);
                maxPrice = Math.max(...values);
            }
            return { ...item, supplier, price: { minPrice, maxPrice } };
        });
        return res;
    }

    async detail(data: ItemDto) {
        const existingItem = await this.itemRepo.findOne({ where: { id: data.id } });
        if (!existingItem) {
            return `Item not found!`;
        }
        
        let category: any[] = [];
        if(existingItem?.categoryId) {
            category = await this.serviceRepo.find({
                where: { id: In(existingItem?.categoryId) },
            });
        }

        const supplier = await this.supplierRepo.findOne({
            where: { id: existingItem.supplierId },
        });

        const price = await this.itemPriceRepo.find({
            where: { itemId: data.id },
        });

        price.sort((a, b) => a.minQty - b.minQty || a.maxQty - b.maxQty || a.price - b.price);

        const comment = await this.commentRepo.find({
            where: { itemId: data.id },
        });

        let avgRate = 0;
        if (comment.length > 0) {
            avgRate = Math.round(comment.reduce((a, b) => a + b.rate, 0) / comment.length);
        }

        let imgs = [];
        if (existingItem.primaryImg) {
            imgs = [...existingItem.primaryImg];
        }
        if (existingItem.images) {
            imgs = [...imgs, ...existingItem.images];
        }

        return {
            ...existingItem,
            images: imgs,
            avgRate,
            totalCmt: comment.length,
            category,
            supplier,
            price
        };
    }

    async related(data: RelatedItemDto) {
        try {
            const item = await this.itemRepo.findOneBy({ id: data.itemId });
            if (!item || !item.categoryId?.length) return [];

            const related = await this.itemRepo.find({
                where: {
                    id: Not(data.itemId),
                    categoryId: Raw((alias) => `${alias} ?| array[:...cats]`, {
                        cats: item.categoryId, 
                    }),
                },
                take: 4,
            });

            const base = new Set(item.categoryId);
            const res = related
                .map(p => ({ p, score: (p.categoryId || []).filter(c => base.has(c)).length }))
                .sort((a, b) => b.score - a.score)
                .map(x => x.p);

            const itemIds = res.map(item => item.id);
            const prices = await this.itemPriceRepo.find({
                where: { itemId: In(itemIds) },
            });
            const finalRes = res.map(item => {
                const itemPrices = prices.filter(p => p.itemId === item.id);
                let minPrice: number | null = null;
                let maxPrice: number | null = null;
                if (itemPrices.length > 0) {
                    const values = itemPrices.map(p => p.price); 
                    minPrice = Math.min(...values);
                    maxPrice = Math.max(...values);
                }
                return { ...item, price: { minPrice, maxPrice } };
            })
            return finalRes;
        } catch (error) {
            throw error;
        }
    }

    async supplierByCategory(data: SupplierByCategoryDto) {
        try {
            const item = await this.itemRepo.findOneBy({ id: data.itemId });
            if (!item || !item.categoryId?.length) return [];

            const related = await this.itemRepo.find({
                where: {
                    supplierId: Not(item.supplierId),
                    categoryId: Raw((alias) => `${alias} ?| array[:...cats]`, {
                        cats: item.categoryId, 
                    }),
                },
                take: 4,
            });
            const itemIds = related.map(item => item.id);
            const prices = await this.itemPriceRepo.find({
                where: { itemId: In(itemIds) },
            });
            const supplierIds = [...new Set(related.map(item => item.supplierId))];
            const suppliers = await this.supplierRepo.findBy({ id: In(supplierIds) });
            return suppliers;
      
        } catch (error) {
            console.log(error);
        }
    }
}
