import { Body, Query } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { ItemService } from './item.service';
import { ItemDto, ItemReq, RelatedItemDto, SupplierByCategoryDto } from './dto/item.dto';

@DefController('item')
export class ItemController {
    constructor(private readonly itemService: ItemService) {}
    @DefGet('list')
    async getAll(@Query() params: ItemReq) {
        return await this.itemService.list(params);
    }

    @DefPost('detail')
    async getDetail(@Body() data: ItemDto) {
        return await this.itemService.detail(data);
    }

    @DefPost('related')
    async getRelated(@Body() data: RelatedItemDto) {
        return await this.itemService.related(data);
    }

    @DefPost('supplier-by-category')
    async supplierByCategory(@Body() data: SupplierByCategoryDto) {
        return await this.itemService.supplierByCategory(data);
    }
}
