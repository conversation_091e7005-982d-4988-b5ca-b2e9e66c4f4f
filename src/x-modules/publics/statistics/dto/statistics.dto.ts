import { ApiProperty } from '@nestjs/swagger';

export class StatisticsResponseDto {
    @ApiProperty({ description: 'Tổng số nhà cung cấp' })
    totalSuppliers: number;

    @ApiProperty({ description: 'Tổng số sản phẩm' })
    totalItems: number;

    @ApiProperty({ description: 'Tổng số báo giá' })
    totalQuotations: number;

    @ApiProperty({ description: 'Số nhà cung cấp hoạt động' })
    activeSuppliers: number;

    @ApiProperty({ description: 'Số sản phẩm hoạt động' })
    activeItems: number;

    @ApiProperty({ description: 'Báo giá mới' })
    newQuotations: number;

    @ApiProperty({ description: 'Báo giá đã hoàn thành' })
    completedQuotations: number;

    @ApiProperty({ description: 'Thống kê theo tháng' })
    monthlyStats: {
        suppliers: { month: string; count: number }[];
        items: { month: string; count: number }[];
        quotations: { month: string; count: number }[];
    };
}

export class StatisticsFilterDto {
    @ApiProperty({ description: 'Từ ngày (YYYY-MM-DD)', required: false })
    fromDate?: string;

    @ApiProperty({ description: 'Đến ngày (YYYY-MM-DD)', required: false })
    toDate?: string;

    @ApiProperty({ description: 'ID nhà cung cấp', required: false })
    supplierId?: string;
}
