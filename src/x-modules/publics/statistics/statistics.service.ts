import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { NSSupplier } from '~/common/enums/supplier.enum';
import { ItemRepo, QuotationRepo, SupplierRepo } from '~/domains/primary';

@Injectable()
export class StatisticsService {
    constructor(
        @InjectRepo(ItemRepo)
        private readonly itemRepo: ItemRepo,
        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(QuotationRepo)
        private readonly quotationRepo: QuotationRepo,
    ) {}

    async getOverallStatistics() {
        // Tổng số item
        // const totalItems = await this.itemRepo.count();
        // // Tổng số supplier hoạt động
        // const totalSuppliers = await this.supplierRepo.count({
        //     where: {
        //         status: NSSupplier.EStatus.ACTIVE,
        //     },
        // });
        // // Tổng số quotation
        // const totalQuotations = await this.quotationRepo.count();
        // RENDOM TOTAL ITEM, SUPPLIER, QUOTATION
        const totalItems = Math.floor(Math.random() * (1000 - 100 + 1) + 100);
        const totalSuppliers = Math.floor(Math.random() * (1000 - 100 + 1) + 100);
        const totalQuotations = Math.floor(Math.random() * (1000 - 100 + 1) + 100);
        return {
            totalItems,
            totalQuotations,
            totalSuppliers,
        };
    }
}
