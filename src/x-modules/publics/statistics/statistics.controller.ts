import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { StatisticsService } from './statistics.service';
import { StatisticsResponseDto } from './dto/statistics.dto';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

@DefController('statistics')
export class StatisticsController {
    constructor(private readonly statisticsService: StatisticsService) {}

    @DefGet('overview')
    @ApiOperation({ summary: 'Lấy thống kê tổng quan' })
    @ApiResponse({
        status: 200,
        description: 'Thống kê tổng quan thành công',
        type: StatisticsResponseDto,
    })
    async getOverallStatistics() {
        return await this.statisticsService.getOverallStatistics();
    }
}
