import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { Body, UploadedFile, UploadedFiles, UseInterceptors } from '@nestjs/common';
import {
    AnyFilesInterceptor,
    FileFieldsInterceptor,
    FileInterceptor,
} from '@nestjs/platform-express';
import { UploadFileService } from './uploadFile.service';

@DefController('uploadFiles')
export class SupplierController {
    constructor(private readonly supplierSevice: UploadFileService) {}

    @DefPost('upload_multiple_s3', {
        summary: 'Hàm upload file lên S3',
    })
    @UseInterceptors(AnyFilesInterceptor())
    uploadMultipleS3(@UploadedFiles() files: Express.Multer.File[]) {
        return this.supplierSevice.uploadMultipleS3(files);
    }

    @DefPost('upload_single_s3')
    @UseInterceptors(FileInterceptor('files'))
    uploadSingleS3(@UploadedFile() file: Express.Multer.File) {
        return this.supplierSevice.uploadSingleS3(file);
    }
}
