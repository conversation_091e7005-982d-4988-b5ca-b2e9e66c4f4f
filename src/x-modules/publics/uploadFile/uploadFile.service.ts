import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { nanoid } from 'nanoid';

@Injectable()
export class UploadFileService {
    AWS_S3_BUCKET_NAME: string;
    s3: AWS.S3;
    constructor() {
        this.AWS_S3_BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || '';
        const ACCESS_KEY_ID = process.env.AWS_S3_ACCESS_KEY_ID || '';
        const SECRET_ACCESS_KEY = process.env.AWS_S3_SECRET_ACCESS_KEY || '';

        this.s3 = new AWS.S3({
            accessKeyId: ACCESS_KEY_ID,
            secretAccessKey: SECRET_ACCESS_KEY,
        });
    }
    async uploadSingleS3(file: Express.Multer.File) {
        const current = new Date();
        let temp: string[] = file?.originalname ? file.originalname.split('.') : [];
        let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : '';
        let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3;
        let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}${ext}`;
        const key = `${LINK_UPLOAD_S3}/${fileName}`;
        const params = {
            Bucket: this.AWS_S3_BUCKET_NAME,
            Key: key, // File name you want to save as in S3
            Body: file.buffer,
            ACL: 'public-read',
            // ContentType: 'image/jpeg',
        };
        return new Promise<any>((resolve, reject) => {
            this.s3.upload(params, (err: any, data: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ fileName, fileUrl: data.Location });
                }
            });
        });
    }
    async uploadMultipleS3(files: Array<Express.Multer.File>) {
        if (!Array.isArray(files)) {
            throw new Error('Files phải là một array');
        }

        const lstTask = [];
        for (const file of files) {
            if (!file || !file.buffer) {
                throw new Error('File không hợp lệ hoặc không có buffer');
            }
            lstTask.push(this.uploadSingleS3(file));
        }

        return Promise.all(lstTask);
    }
}
