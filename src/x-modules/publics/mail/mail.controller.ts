import { Body } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { EmailService } from '~/x-modules/@global/mail/send-mail.service';
import { SendMailTemplateDto } from './send-mail.dto';

@DefController('mail')
export class EmailController {
    constructor(private readonly emailService: EmailService) {}

    // Gửi email
    @DefGet('send-mail', {
        summary: 'Hàm gửi email',
    })
    sendMail() {
        return this.emailService.sendMail();
    }

    // Gửi email với template
    @DefPost('send-mail-template', {
        summary: 'Hàm gửi email',
    })
    async sendMailTemplate(@Body() params: SendMailTemplateDto) {
        return await this.emailService.sendMailWithTemplate(params);
    }
}
