import { Body, Query } from '@nestjs/common';
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { SupplierService } from './supplier.service';

@DefController('supplier')
export class SupplierController {
    constructor(private readonly supplierService: SupplierService) {}

    @DefPost('detail')
    async getDetail(@Body() data: { supplierId: string }) {
        return await this.supplierService.detail(data);
    }

    @DefGet('list')
    async getList() {
        return await this.supplierService.list();
    }

    @DefPost('store')
    async store(@Body() data: { supplierId: string }) {
        return await this.supplierService.store(data);
    }

    @DefGet('store-item')
    async storeItem(@Query() params: any) {
        return await this.supplierService.storeItem(params);
    }

    @DefGet('get-all', {
        summary: '<PERSON>àm lấy danh sách supplier',
    })
    async list(@Query() params: any) {
        return await this.supplierService.getAll(params);
    }



}
