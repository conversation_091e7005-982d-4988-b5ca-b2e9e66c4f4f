import { Injectable } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ItemRepo, QuotationRepo, ServiceRepo, SupplierRepo } from '~/domains/primary';
import { ILike, In, Not, Raw } from 'typeorm';
import { CommentRepo } from '~/domains/primary/comment/comment.repo';
import { NSSupplier } from '~/common/enums/supplier.enum';
import { SupplierOpacityRepo } from '~/domains/primary/supplier-opacity/supplier-opacity.repo';
import { SupplierLawRepo } from '~/domains/primary/supplier-law/supplier-law.repo';
import { ItemPriceRepo } from '~/domains/primary/item-price/item-price.repo';
import { memberSessionContext } from '~/x-modules/client/member-session.context';
import { NSQuotation } from '~/common/enums/quotation.enum';
@Injectable()
export class SupplierService {
    constructor(
        @InjectRepo(ItemRepo)
        private readonly itemRepo: ItemRepo,

        @InjectRepo(CommentRepo)
        private readonly commentRepo: CommentRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,

        @InjectRepo(SupplierLawRepo)
        private readonly supplierLawRepo: SupplierLawRepo,

        @InjectRepo(SupplierOpacityRepo)
        private readonly supplierOpacityRepo: SupplierOpacityRepo,

        @InjectRepo(ServiceRepo)
        private readonly serviceRepo: ServiceRepo,

        @InjectRepo(ItemPriceRepo)
        private readonly itemPriceRepo: ItemPriceRepo,

        @InjectRepo(QuotationRepo)
        private readonly quotationRepo: QuotationRepo,
    ) {}

    async detail(data: { supplierId: string }) {
        const supplier = await this.supplierRepo.findOne({
            where: {
                id: data.supplierId,
            },
        });

        const item = await this.itemRepo.find({
            where: {
                supplierId: data.supplierId,
            },
        });

        const itemIds = item.map(item => item.id);

        const comment = await this.commentRepo.find({
            where: {
                itemId: In(itemIds),
            },
        });
        let avgRate = 0;
        avgRate = Math.round(comment.reduce((a, b) => a + b.rate, 0) / comment.length);

        const supplierLaw = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplier?.id,
            },
        });

        const supplierOpacity = await this.supplierOpacityRepo.findOne({
            where: {
                supplierId: supplier?.id,
            },
        });

        if (!supplierLaw) {
            return {
                ...supplier,
                ...supplierOpacity,
            };
        }

        if (!supplierOpacity) {
            return {
                ...supplier,
                ...supplierLaw,
            };
        }

        if (!supplierLaw && !supplierOpacity) {
            return supplier;
        }

        let category: any[] = [];
        if (supplierOpacity?.businessAreas) {
            category = await this.serviceRepo.find({
                where: { id: In(supplierOpacity?.businessAreas) },
            });
        }

        return {
            ...supplier,
            ...supplierLaw,
            ...supplierOpacity,
            category,
            avgRate,
            totalComment: comment.length,
        };
    }

    async list() {
        return await this.supplierRepo.find({
            where: { status: NSSupplier.EStatus.ACTIVE },
        });
    }

      async store(data: { supplierId: string }) {
        const supplier = await this.supplierRepo.findOne({
            where: { id: data.supplierId },
        });

        if (!supplier) {
            return { message: 'Supplier not found' };
        }
        
        const item = await this.itemRepo.find({
            where: {
                supplierId: data.supplierId,
            },
        });

        const itemIds = item.map(item => item.id);

        const comment = await this.commentRepo.find({
            where: {
                itemId: In(itemIds),
            },
        });
        let avgRate = 0;
        avgRate = Math.round(comment.reduce((a, b) => a + b.rate, 0) / comment.length);

        const supplierLaw = await this.supplierLawRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        const supplierOpacity = await this.supplierOpacityRepo.findOne({
            where: {
                supplierId: supplier.id,
            },
        });

        let businessAreas: any[] = [];
        if (supplierOpacity?.businessAreas) {
            businessAreas = await this.serviceRepo.find({
                where: { id: In(supplierOpacity?.businessAreas) },
            });
        }
        const totalQuote = await this.quotationRepo.count({
            where: {
                supplierReceiverId: supplier.id,
                status: NSQuotation.EStatus.QUOTED,
            },
        });
        // số lượng báo giá thàh công của supplier
        // doanh thu random từ 100.000.000 - 1.000.000.000
        const totalRevenue = Math.floor(Math.random() * (1000000000 - 100000000 + 1) + 100000000);
        // đơn hàng random từ 100 - 1000
        const totalOrder = Math.floor(Math.random() * (1000 - 100 + 1) + 100);
        // sản phẩm



        return {
            ...supplier,
            ...supplierLaw,
            ...supplierOpacity,
            id: supplier.id,
            businessAreas,
            totalItem: item.length,
            totalQuote: totalQuote,
            totalOrder: totalOrder,
            totalRevenue: totalRevenue,
            avgRate,
            totalComment: comment.length,
        };

    }

    async storeItem(params: any) {
        const res = await this.itemRepo.findPagination(
            {
            where: { supplierId: params.supplierId, isActive: true },
            order: { id: 'DESC' },
            },
            params,
        );

        const supplierIds = res.data.map(item => item.supplierId);
        const suppliers = await this.supplierRepo.findBy({
            id: In(supplierIds),
        });
        const itemIds = res.data.map(item => item.id);
        const prices = await this.itemPriceRepo.find({
            where: { itemId: In(itemIds) },
        });
        res.data = res.data.map(item => {
            const supplier = suppliers.find(supplier => supplier.id === item.supplierId);
            const itemPrices = prices.filter(p => p.itemId === item.id);
            let minPrice: number | null = null;
            let maxPrice: number | null = null;
            if (itemPrices.length > 0) {
                const values = itemPrices.map(p => p.price); 
                minPrice = Math.min(...values);
                maxPrice = Math.max(...values);
            }
            return { ...item, supplier, price: { minPrice, maxPrice } };
        });
        return res;
    }

    async getAll(params: any) {
        const whereCon: any = {};

        if (params.searchText?.trim()) {
            whereCon.fullName = ILike(`%${params.searchText.trim()}%`);
        }
        if (params.categoryIds) {
            const categoryIds = Array.isArray(params.categoryIds)
            ? params.categoryIds.map((s) => String(s).trim())
            : String(params.categoryIds).split(',').map((s) => s.trim());

            if (categoryIds.length > 0) {
            const matchedOpacity = await this.supplierOpacityRepo.find({
                select: ['supplierId'],
                where: {
                businessAreas: Raw(
                    (alias) => `${alias} ?| ARRAY[:...categoryIds]::text[]`,
                    { categoryIds },
                ),
                },
            });

            const supplierIdSet = Array.from(
                new Set(matchedOpacity.map((x) => x.supplierId)),
            );

            if (supplierIdSet.length === 0) {
                return { total: 0, data: [] };
            }

            whereCon.id = In(supplierIdSet);
            }
        }

        const res = await this.supplierRepo.findPagination(
            {
            where: { ...whereCon },
            order: { id: 'DESC' },
            },
            params,
        );

        const supplierIds = res.data.map((item) => item.id);

        const [suppliersLaw, suppliersOpacity] = await Promise.all([
            this.supplierLawRepo.findBy({ supplierId: In(supplierIds) }),
            this.supplierOpacityRepo.findBy({ supplierId: In(supplierIds) }),
        ]);

        const result = {
            total: res.total,
            data: res.data.map((item) => {
            const supplierLaw = suppliersLaw.find((s) => s.supplierId === item.id);
            const supplierOpacity = suppliersOpacity.find((s) => s.supplierId === item.id);
            return {
                ...item,
                ...(supplierLaw ?? {}),
                ...(supplierOpacity ?? {}),
            };
            }),
        };

        return result;
    }

}
