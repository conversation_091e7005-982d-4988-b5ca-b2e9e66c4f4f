import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';

import { Body } from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/category.dto';

@DefController('category')
export class CategoryController {
    constructor(private readonly categoryService: CategoryService) {}
    @DefPost('create', {
        summary: 'Hàm tạo category',
    })
    async insert(@Body() data: CreateCategoryDto) {
        return await this.categoryService.insert(data);
    }
    @DefGet('get-all', {
        summary: 'Hàm lấy danh sách category',
    })
    async getAll() {
        return await this.categoryService.getAll();
    }
}
