import { InjectRepo } from 'nestjs-typeorm3-kit';
import { Injectable, NotFoundException } from '@nestjs/common';
import { ServiceEntity, ServiceRepo, SupplierRepo } from '~/domains/primary';
import { CreateCategoryDto } from './dto/category.dto';
import { ILike, Raw } from 'typeorm';

@Injectable()
export class CategoryService {
    constructor(
        @InjectRepo(ServiceRepo)
        private readonly serviceRepo: ServiceRepo,

        @InjectRepo(SupplierRepo)
        private readonly supplierRepo: SupplierRepo,
    ) {}
    async insert(data: CreateCategoryDto) {
        try {
            const exitingCategory = await this.serviceRepo.findOne({
                where: { name: <PERSON><PERSON>(data.name) },
            });
            if (exitingCategory) {
                return 'Danh mục đã tồn tại';
            }
            let level = 1;
            let parent: ServiceEntity | null = null;

            if (data.parentId) {
                parent = await this.serviceRepo.findOne({ where: { id: data.parentId } });
                if (!parent) {
                    throw new NotFoundException('<PERSON>hông tìm thấy danh mục cha.');
                }
                level = (parent.level ?? 0) + 1;
            }

            const entity = this.serviceRepo.create({
                ...data,
                code: data.code,
                name: data.name,
                level,
                isLast: true,
            });

            const saved = await this.serviceRepo.save(entity);

            if (parent && parent.isLast === true) {
                await this.serviceRepo.update(parent.id, { isLast: false });
            }

            return saved;
        } catch (error) {
            throw error;
        }
    }

   async getAll() {
    try {
        const categories = await this.serviceRepo.find({
        where: { level: 1 },
        
        });

        const res = await this.serviceRepo.find({
        where: { isActive: true },
        });

        const mapCategories = categories.map(category => {
        return {
            ...category,
            children: res
            .filter(item => item.parentId === category.id)
            .map(item => ({
                value: item.id,
                title: item.name,
            })),
        };
        });

        return mapCategories;
    } catch (error) {
        throw error;
    }
    }



}
