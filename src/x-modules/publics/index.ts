import { ChildModule } from 'nestjs-typeorm3-kit';
import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { lazyLoadClasses } from '~/utils/tech.util';
import { join } from 'path';
import { EmailService } from '~/x-modules/@global/mail/send-mail.service';
import { RefixModule } from '~/x-modules/config-module';
const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);
@ChildModule({
    prefix: RefixModule.publics,
    imports: [PrimaryRepoModule],
    providers: [...services, EmailService],
    controllers: [...controllers],
})
export class PublicModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
