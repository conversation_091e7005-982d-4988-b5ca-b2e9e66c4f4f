import { Injectable } from '@nestjs/common';
import { MailOptions, MailTemplateData } from './mail.types';
import * as fs from 'fs';
import * as path from 'path';
import * as hbs from 'handlebars';
import { SendMailTemplateDto } from '~/x-modules/publics/mail/send-mail.dto';

require('dotenv').config();
const nodemailer = require('nodemailer');

@Injectable()
export class EmailService {
    constructor() {}

    // Tạo transporter để gửi email
    async createTransporter() {
        try {
            const transporter = nodemailer.createTransport({
                service: 'gmail',
                auth: {
                    user: process.env.GOOGLE_MAIL_USER,
                    pass: process.env.GOOGLE_APP_PASSWORD,
                },
            });

            return transporter;
        } catch (err) {
            console.error('Error creating transporter:', err);
            throw err; // Rethrow the error for further handling
        }
    }

    // Gửi email với transporter đã tạo
    async sendMailPro(mailOption: MailOptions) {
        try {
            let emailTransporter = await this.createTransporter();
            return await emailTransporter.sendMail({
                from: process.env.GOOGLE_MAIL_USER,
                ...mailOption,
            });
        } catch (err) {
            console.error('Error sending email:', err);
            throw err;
        }
    }

    // Gửi email với template
    async sendMailWithTemplate(options: SendMailTemplateDto) {
        try {
            const { templateName, data, to, subject } = options;
            // Sửa đường dẫn để tương thích với production
            let templatePath: string;
            if (process.env.NODE_ENV === 'production') {
                // Trong production, template nằm trong thư mục dist
                templatePath = path.join(process.cwd(), 'dist/template-mail', templateName);
            } else {
                // Trong development, template nằm trong thư mục src
                templatePath = path.join(process.cwd(), 'src/template-mail', templateName);
            }
            const templateContent = fs.readFileSync(templatePath, 'utf8');
            const template = hbs.compile(templateContent);
            const htmlContent = template(data);

            return await this.sendMailPro({
                to,
                subject,
                html: htmlContent,
            });
        } catch (error) {
            console.error('Error sending email with template:', error);
            throw error;
        }
    }

    // Gửi email với template
    async sendMail() {
        try {
            const mailOptions = {
                from: process.env.GOOGLE_MAIL_USER,
                to: '<EMAIL>',
                subject: 'Test HTML Email',
                html: '<h1>Hi!</h1><p>This is a test email with <strong>HTML</strong> content.</p>',
            };
            const mailOptionsC: any = mailOptions;
            let emailTransporter = await this.createTransporter();
            // console.log('Email sent successfully')
            return await emailTransporter.sendMail(mailOptionsC);
        } catch (err) {
            console.error('Error sending email:', err);
            throw err; // Rethrow the error for further handling
        }
    }
}
