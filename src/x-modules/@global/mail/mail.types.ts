export interface MailOptions {
  from?: string;
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: MailAttachment[];
}

export interface MailAttachment {
  filename: string;
  content?: string | Buffer;
  path?: string;
  contentType?: string;
}

export interface MailTemplateData {
  [key: string]: any;
}

export interface MailTemplate {
  subject: string;
  template: string;
  data: MailTemplateData;
}

export enum MailTemplateType {
  WELCOME = 'welcome',
  RESET_PASSWORD = 'reset-password',
  VERIFY_EMAIL = 'verify-email',
  ORDER_CONFIRMATION = 'order-confirmation',
  PAYMENT_SUCCESS = 'payment-success',
  PAYMENT_FAILED = 'payment-failed',
  SUBSCRIPTION_EXPIRED = 'subscription-expired',
  NEWSLETTER = 'newsletter',
}
