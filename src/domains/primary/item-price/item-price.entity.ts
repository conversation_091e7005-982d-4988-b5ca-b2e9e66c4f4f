import { Column, Entity } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty } from "@nestjs/swagger";

@Entity('item-price')
export class ItemPriceEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Id của sản phẩm' })
    @Column({ type: 'varchar', nullable: true })
    itemId: string;

    @ApiProperty({ description: 'Giá từ' })
    @Column({ nullable: true })
    minQty: number;

    @ApiProperty({ description: 'Giá đến' })
    @Column({ nullable: true })
    maxQty: number;

    @ApiProperty({ description: '<PERSON>i<PERSON> bán' })
    @Column({ nullable: true })
    price: number;
}