import { ApiProperty } from '@nestjs/swagger';
import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
import { ChatMsgType } from '~/common/enums/message.enum';

@Entity('chat_message')
export class ChatMessageEntity {
    @ApiProperty({ description: 'Id của tin nhắn' })
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ApiProperty({ description: 'Id của thread' })
    @Column({ nullable: true })
    threadKey: string;

    @ApiProperty({ description: 'Id của sản phẩm' })
    @Column({ nullable: true })
    productId: string;

    @ApiProperty({ description: 'Id của người gửi' })
    @Column({ nullable: true })
    senderId: string;

    @ApiProperty({ description: 'Id của người nhận' })
    @Column({ nullable: true })
    receiverId: string;

    @ApiProperty({ description: '<PERSON>ạ<PERSON> tin nhắn' })
    @Column({ nullable: true })
    messageType: ChatMsgType;

    @ApiProperty({ description: 'Nội dung tin nhắn' })
    @Column({ nullable: true })
    text?: string;

    @ApiProperty({ description: 'Url file' })
    @Column({ nullable: true })
    fileUrl?: string;

    @ApiProperty({ description: 'Thời gian đọc' })
    @Column({ nullable: true })
    readAt?: Date;

    @ApiProperty({ description: 'Thời gian tạo' })
    @Column({ nullable: true })
    createdAt: Date;

    @ApiProperty({ description: 'Thời gian cập nhật' })
    @Column({ nullable: true })
    updatedAt: Date;

    @ApiProperty({ description: 'Trạng thái xóa' })
    @Column({ nullable: true, default: false })
    isDeleted: boolean;
}
