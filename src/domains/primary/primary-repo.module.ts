import { Module } from '@nestjs/common';
import { join, normalize } from 'path';
import { DefRepositoryModule } from 'nestjs-typeorm3-kit';
import { PRIMARY_CONNECTION_NAME } from '~/databases/constants';

@Module({
    imports: [
        DefRepositoryModule.forRootAsync({
            useFactory: () => ({
                globPattern: normalize(join(__dirname, '../**/*.repo.{ts,js}'))?.replace(
                    /\\/g,
                    '/',
                ),
                dataSource: PRIMARY_CONNECTION_NAME,
            }),
        }),
    ],
    exports: [DefRepositoryModule],
})
export class PrimaryRepoModule {}
