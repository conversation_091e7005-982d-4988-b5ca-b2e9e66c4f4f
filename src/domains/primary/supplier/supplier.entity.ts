import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSSupplier } from '~/common/enums/supplier.enum';

@Entity('supplier')
export class SupplierEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Mã đoanh nghiệp' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    code: string;

    @ApiProperty({ description: 'Mã tenant' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    tenantId: string;

    @ApiProperty({ description: 'Tên doanh nghiệp' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    fullName: string;

    @ApiProperty({ description: 'Logo doanh nghiệp' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    logoUrl: string;

    @ApiProperty({ description: 'Trạng thái' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    status: NSSupplier.EStatus;

    @ApiProperty({ description: 'Địa chỉ doanh nghiệp' })
    @Column({
        type: 'varchar',

        nullable: true,
    })
    address: string;

    @ApiProperty({ description: 'Mã số thuế' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    taxCode: string;

    @ApiProperty({ description: 'Website Url' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    websiteUrl: string;

    @ApiProperty({ description: 'Số điện thoại' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    phone: string;

    @ApiProperty({ description: 'Email' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    email: string;

    // isAuth
    @ApiProperty({ description: 'isAuth' })
    @Column({
        type: 'boolean',
        nullable: true,
        default: false,
    })
    isAuth: boolean;

    @ApiProperty({ description: 'accountId' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    accountId: string;
}
