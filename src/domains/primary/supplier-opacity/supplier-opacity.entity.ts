import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('supplier-opacity')
export class SupplierOpacityEntity extends PrimaryBaseEntity {
    // Lĩnh vực kinh doanh
    @ApiProperty({ description: 'Lĩnh vực kinh doanh' })
    @Column('jsonb', { nullable: true })
    businessAreas: any[];
    // Chứng nhận giải thưởng
    @ApiProperty({ description: 'Chứng nhận giải thưởng' })
    @Column('jsonb', { nullable: true })
    certificatesFiles: any[];
    // Doanh thu gần đây
    @ApiProperty({ description: 'Doanh thu gần đây' })
    @Column('jsonb', { nullable: true })
    recentRevenue: any[];
    // Báo cáo tài chính
    @ApiProperty({ description: '<PERSON><PERSON><PERSON> c<PERSON>o tài ch<PERSON>' })
    @Column('jsonb', { nullable: true })
    financialReports: any[];
    // Tài liệu tham khảo
    @ApiProperty({ description: 'Tài liệu tham khảo' })
    @Column('jsonb', { nullable: true })
    referenceDocuments: any[];
}
