import { Column, Entity } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty } from "@nestjs/swagger";

@Entity('comment')
export class CommentEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Id of the item' })
    @Column({ nullable: true })
    itemId: string;

    @ApiProperty({ description: 'Id of the supplier' })
    @Column({ nullable: true })
    supplierId: string;

    @ApiProperty({ description: 'Content of the comment' })
    @Column({ nullable: true })
    content: string;

    @ApiProperty({ description: 'Rate of the comment' })
    @Column({ nullable: true })
    rate: number;
}