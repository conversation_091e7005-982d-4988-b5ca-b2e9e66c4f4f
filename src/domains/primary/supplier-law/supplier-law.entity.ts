import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('supplier-law')
export class SupplierLawEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: '<PERSON><PERSON><PERSON> hình hồ sơ' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    profileTypes: string;

    @ApiProperty({ description: 'Loại hình công ty' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    companyTypes: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> cấp ĐKKD' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    dateOfIssue: string;

    @ApiProperty({ description: 'Nơi cấp ĐKKD' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    placeOfIssue: string;

    @ApiProperty({ description: '<PERSON><PERSON><PERSON> s<PERSON>' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    profileName: string;

    @ApiProperty({ description: 'Địa chỉ theo ĐKKD' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    addressOfIssue: string;

    @ApiProperty({ description: 'Địa chỉ giao dịch' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    dealAddress: string;

    @ApiProperty({ description: 'Vốn điều lệ' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    capital: string;

     @ApiProperty({ description: 'Tài sản cố định (tỷ đồng)' })
    @Column({
        type: 'float',
        nullable: true,
    })
    assets: number;

    @ApiProperty({ description: 'Fax' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    fax: string;

    @ApiProperty({ description: 'Ngày thành lập' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    dateOfEstablishment: string;

    @ApiProperty({ description: 'Họ và tên người đại diện' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    legalRepName: string;

    @ApiProperty({ description: 'Số điện thoại người đại diện' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    legalRepPhone: string;

    @ApiProperty({ description: 'Email người đại diện' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    legalRepEmail: string;

    @ApiProperty({ description: 'Chức vụ người đại diện' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    legalRepPosition: string;

    @ApiProperty({ description: 'File pháp lý' })
    @Column('jsonb', { nullable: true })
    legalFile: any[];
}
