import { Column, Entity } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty } from "@nestjs/swagger";
import { NSQuotation } from "~/common/enums/quotation.enum";

@Entity('quotation')
export class QuotationEntity extends PrimaryBaseEntity {
    @ApiProperty({description: 'Id của nhà cung cấp'})
    @Column({nullable: true})
    supplierReceiverId: string;

    @ApiProperty({description: 'Id của sản phẩm'})
    @Column({nullable: true})
    itemId: string;
    // quantity
    @ApiProperty({description: 'Số lượng'})
    @Column({nullable: true})
    quantity: number;

    // supplierRequestId
    @ApiProperty({description: 'Id của nhà cung cấp yêu cầu'})
    @Column({nullable: true})
    supplierRequestId: string;
    // status

    @ApiProperty({description: 'Trạng thái'})
    @Column({nullable: true, default: NSQuotation.EStatus.NEW})
    status: string;
    // shippingAdress
    @ApiProperty({description: 'Địa chỉ giao hàng'})
    @Column({nullable: true})
    deliveryAddress: string;
    // deliveryTime

    @ApiProperty({description: 'Thời gian giao hàng'})
    @Column({nullable: true})
    deliveryDate: string;

    // Phương thức giao hàng
    @ApiProperty({description: 'Phương thức giao hàng'})
    @Column({nullable: true})
    deliveryMethod: string;

    // incoterm
    @ApiProperty({description: 'Điều khoản giao hàng'})
    @Column({nullable: true})
    incoterm: string;
    // paymentTerm
    @ApiProperty({description: 'Điều khoản thanh toán'})
    @Column({nullable: true})
    paymentTerm: string;
    // note
    @ApiProperty({description: 'Ghi chú'})
    @Column({nullable: true})
    note: string;
    // otherRequest
    @ApiProperty({description: 'Yêu cầu khác'})
    @Column({nullable: true})
    otherRequest: string;
    // requestCode
    @ApiProperty({description: 'Mã yêu cầu'})
    @Column({nullable: true})
    requestCode: string;
    // typeQuotation
    @ApiProperty({description: 'Loại báo giá'})
    @Column({nullable: true})
    typeQuotation: string;
    // quotationCode
    @ApiProperty({description: 'Mã báo giá'})
    @Column({nullable: true})
    quotationCode: string;

    @ApiProperty({description: 'Đơn vị'})
    @Column({nullable: true})
    unit: string;

    @ApiProperty({description: 'Phí vận chuyển'})
    @Column({nullable: true})
    deliveryCost: number;

    @ApiProperty({description: 'Tổng tiền'})
    @Column({nullable: true})
    totalPrice: number;

  @ApiProperty({
    description: 'File báo giá',
    type: 'object',
    properties: {
        uid: { type: 'string', example: '1' },
        name: { type: 'string', example: 'bao-gia.pdf' },
        url: { type: 'string', example: 'https://example.com/bao-gia.pdf' }
    },
    example: {
        uid: '1',
        name: 'bao-gia.pdf',
        url: 'https://example.com/bao-gia.pdf'
    }
  })
  @Column({ type: 'json', nullable: true })
  quotationFile: {
    uid: string;
    name: string;
    url: string;
  };

    @ApiProperty({description: 'Hạn báo giá'})
    @Column({nullable: true})
    quoteValidity: string;
}