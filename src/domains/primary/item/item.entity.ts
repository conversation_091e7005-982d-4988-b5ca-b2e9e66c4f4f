import { Enti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { ServiceEntity } from '../service/service.entity';

@Entity('item')
export class ItemEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Tên sản phẩm' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    name: string;

    @ApiProperty({ description: 'Mã sản phẩm' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    code: string;

    @ApiProperty({ description: '<PERSON>ô tả' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    note: string;

    @ApiProperty({ description: 'Đơn vị' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    unit: string;

    // @ApiProperty({ description: '<PERSON><PERSON><PERSON> từ' })
    // @Column({
    //     nullable: true,
    // })
    // priceFrom: number;

    // @ApiProperty({ description: '<PERSON><PERSON><PERSON> đến' })
    // @Column({
    //     nullable: true,
    // })
    // priceTo: number;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    description: string;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    shortDesc: string;

    @ApiProperty({ description: 'Đơn hàng tối thiểu' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    minOrder: number;

    @ApiProperty({ description: 'Năng lực cung cấp' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    supplyCapacity: number;

    @ApiProperty({ description: 'Trạng thái' })
    @Column({
        type: 'varchar',
        nullable: true,
        default: true,
    })
    isActive: boolean;

    @ApiPropertyOptional({
        description: 'Tài liệu',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                uid: { type: 'string', example: '1' },
                name: { type: 'string', example: 'anh1.png' },
                url: { type: 'string', example: 'https://example.com/bao-gia.pdf' },
            },
        },
    })
    @Column('jsonb', { nullable: true })
    urlFile: Array<{
        uid: string;
        name: string;
        url: string;
    }>;

    @ApiProperty({
        description: 'Hình ảnh',
        type: 'object',
        properties: {
            uid: { type: 'string', example: '1' },
            name: { type: 'string', example: 'anh1.png' },
            url: { type: 'string', example: 'https://example.com/bao-gia.pdf' },
        },
        example: {
            uid: '1',
            name: 'anh1.png',
            url: 'https://example.com/bao-gia.pdf',
        },
    })
    @Column({ type: 'json', nullable: true })
    primaryImg: Array<{
        uid: string;
        name: string;
        url: string;
    }>;

    @ApiProperty({ description: 'Danh sách hình ảnh khác' })
    @Column('jsonb', { nullable: true })
    images: Array<{
        uid: string;
        name: string;
        url: string;
    }>;

    @ApiProperty({ description: 'Thông số kĩ thuật' })
    @Column('jsonb', { nullable: true })
    technicalSpecs: Array<{
        key: string;
        value: string;
    }>;

    @ApiProperty({ description: 'Nhà cung cấp' })
    @Column({
        type: 'varchar',
        nullable: true,
    })
    supplierId: string;

    @ApiProperty({ description: 'Danh mục' })
    @Column({
        type: 'jsonb',
        nullable: true,
    })
    categoryId: any[];

}
