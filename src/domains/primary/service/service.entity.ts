import { Entity, Column, OneToMany } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { ItemEntity } from '../item/item.entity';

@Entity('service')
export class ServiceEntity extends PrimaryBaseEntity {
  @Column({ type: 'varchar', nullable: false })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  code: string;

  @Column({ nullable: true })
  level: number;

  @Column({ nullable: true, default: false })
  isLast: boolean;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  parentId: string;

  @Column({ type: 'boolean', nullable: true, default: true })
  isActive: boolean;

}
