import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSNotification } from '~/common/enums/notification.enum';

@Entity('notification')
export class NotificationEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'ID người nhận thông báo' })
    @Column({ nullable: false })
    receiverId: string;

    @ApiProperty({ description: 'ID người gửi thông báo' })
    @Column({ nullable: true })
    senderId: string;

    @ApiProperty({ description: 'Loại thông báo', enum: NSNotification.EType })
    @Column({ type: 'varchar', nullable: false })
    type: NSNotification.EType;

    @ApiProperty({ description: 'Tiêu đề thông báo' })
    @Column({ type: 'varchar', nullable: false })
    title: string;

    @ApiProperty({ description: '<PERSON><PERSON> tả thông báo' })
    @Column({ type: 'text', nullable: true })
    description: string;

    @ApiProperty({ description: 'Thời gian gửi thông báo' })
    @Column({ type: 'timestamp', nullable: true })
    sendDate: Date;

    @ApiProperty({ description: 'Trạng thái đọc', enum: NSNotification.EStatus })
    @Column({ type: 'varchar', nullable: false, default: NSNotification.EStatus.UNREAD })
    status: NSNotification.EStatus;

    @ApiProperty({ description: 'ID tham chiếu (quotationId, messageId, etc.)' })
    @Column({ nullable: true })
    referenceId: string;

    @ApiProperty({ description: 'Loại tham chiếu (quotation, message, etc.)' })
    @Column({ type: 'varchar', nullable: true })
    referenceType: string;

    @ApiProperty({ description: 'Dữ liệu bổ sung (JSON)' })
    @Column({ type: 'json', nullable: true })
    metadata: any;
    s;
}
