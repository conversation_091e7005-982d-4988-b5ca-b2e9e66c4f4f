/// <reference types="node" />

declare namespace NodeJS {
    interface ProcessEnv {
        PORT: number;
        REQUEST_TIMEOUT: number;
        // Swagger Config
        SWAGGER_TITLE: string;
        SWAGGER_DESCRIPTION: string;
        SWAGGER_VERSION: string;
        // DB Config
        DB_PRIMARY_TYPE: 'postgres' | 'mssql' | 'mysql';
        DB_PRIMARY_HOST: string;
        DB_PRIMARY_PORT: number;
        DB_PRIMARY_USERNAME: string;
        DB_PRIMARY_PASSWORD: string;
        DB_PRIMARY_DATABASE: string;
        DB_PRIMARY_SYNCHRONIZE: boolean;
        DB_PRIMARY_SSL: boolean;
        DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: boolean;
        // JWT HS256 config
        JWT_SECRET: string;
        JWT_EXPIRY: string;
        JWT_REFRESH_TOKEN_SECRET: string;
        JWT_REFRESH_TOKEN_EXPIRY: string;
        // GOOGLE AUTH
        GOOGLE_CLIENT_ID: string;
        GOOGLE_CLIENT_SECRET: string;
        GOOGLE_CALLBACK_URL: string;
        // APE SSO
        APE_SSO_URL: string;
        APE_CLIENT_ID: string;
        APE_CLIENT_SECRET: string;
        APE_CALLBACK_URL: string;
        // AWS S3
        AWS_S3_BUCKET_NAME: string;
        AWS_S3_ACCESS_KEY_ID: string;
        AWS_S3_SECRET_ACCESS_KEY: string;
        LINK_UPLOAD_S3: string;
        // Redis
        REDIS_URL: string;
    }
}
