<html lang='vi'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title><PERSON><PERSON><PERSON> cầu hỗ trợ khách hàng</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
        color: #333333;
        line-height: 1.6;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        padding: 40px 60px;
      }

      .logo {
        text-align: center;
        margin-bottom: 40px;
      }

      .logo-text {
        font-size: 32px;
        font-weight: bold;
        color: #6c63ff;
        text-decoration: none;
        display: inline-block;
      }

      .greeting {
        font-size: 18px;
        color: #333333;
        margin-bottom: 10px;
        font-weight: normal;
      }

      .title {
        font-size: 24px;
        font-weight: 600;
        color: #333333;
        margin-bottom: 30px;
        line-height: 1.3;
      }

      .content-text {
        font-size: 16px;
        color: #666666;
        line-height: 1.6;
        margin-bottom: 30px;
      }

      .support-details {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 24px;
        margin: 30px 0;
      }

      .detail-row {
        display: flex;
        margin-bottom: 16px;
        align-items: flex-start;
      }

      .detail-row:last-child {
        margin-bottom: 0;
      }

      .detail-label {
        font-weight: 600;
        color: #333333;
        min-width: 80px;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .detail-value {
        color: #333333;
        flex: 1;
        word-break: break-word;
      }

      .detail-value a {
        color: #6c63ff;
        text-decoration: none;
      }

      .detail-value a:hover {
        text-decoration: underline;
      }

      .message-block {
        background-color: #ffffff;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }

      .message-label {
        font-weight: 600;
        color: #333333;
        margin-bottom: 12px;
        font-size: 16px;
      }

      .message-text {
        color: #666666;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
      }

      .action-button {
        text-align: center;
        margin: 40px 0;
      }

      .btn {
        display: inline-block;
        background-color: #6c63ff;
        color: #ffffff !important;
        text-decoration: none;
        padding: 14px 32px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 16px;
        transition: background-color 0.3s ease;
      }

      .btn:hover {
        background-color: #5a52e5;
      }

      .urgent-tag {
        display: inline-block;
        background-color: #ff6b6b;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 8px;
      }

      .footer {
        margin-top: 50px;
        padding-top: 30px;
        border-top: 1px solid #e1e5e9;
        text-align: center;
      }

      .footer-text {
        font-size: 14px;
        color: #999999;
        margin-bottom: 20px;
      }

      .footer-link {
        color: #6c63ff;
        text-decoration: none;
        font-weight: 600;
      }

      .footer-link:hover {
        text-decoration: underline;
      }

      .timestamp {
        font-size: 14px;
        color: #999999;
        font-style: italic;
        margin-bottom: 20px;
      }

      @media (max-width: 640px) {
        .email-container {
          padding: 20px 24px;
        }

        .logo-text {
          font-size: 28px;
        }

        .title {
          font-size: 20px;
        }

        .support-details {
          padding: 20px;
        }

        .detail-row {
          flex-direction: column;
        }

        .detail-label {
          margin-bottom: 4px;
          margin-right: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class='email-container'>
      <!-- Logo -->
      <div class='logo'>
        <span class='logo-text'>🔥 ApeTechs Platform SME 360</span>
      </div>

      <!-- Greeting -->
      <div class='greeting'>Xin chào,</div>

      <!-- Title -->
      <div class='title'>Chúng tôi đã nhận được yêu cầu hỗ trợ của bạn.</div>

      <!-- Content -->
      <div class='content-text'>
        Cám ơn bạn đã liên hệ với ApeTechs Platform SME 360. Đội ngũ hỗ trợ của chúng tôi sẽ xem xét
        và phản hồi yêu cầu của bạn trong thời gian sớm nhất.
      </div>

      <!-- Support Details -->
      <div class='support-details'>
        <div class='detail-row'>
          <div class='detail-label'>Họ tên:</div>
          <div class='detail-value'>{{name}}</div>
        </div>

        <div class='detail-row'>
          <div class='detail-label'>Số điện thoại:</div>
          <div class='detail-value'>{{phone}}</div>
        </div>

        <div class='detail-row'>
          <div class='detail-label'>Email:</div>
          <div class='detail-value'>
            <a href='mailto:{{email}}'>{{email}}</a>
          </div>
        </div>

        <div class='detail-row'>
          <div class='detail-label'>Chủ đề:</div>
          <div class='detail-value'>
            {{subject}}
          </div>
        </div>
      </div>

      <!-- Message -->
      <div class='message-block'>
        <div class='message-label'>Nội dung tin nhắn:</div>
        <div class='message-text'>{{message}}</div>
      </div>

      <!-- Footer -->
      <div class='footer'>
        <div class='footer-text'>
          Email này được tạo tự động từ hệ thống ApeTechs Platform SME 360.<br />
          Vui lòng không trả lời trực tiếp email này.
        </div>
      </div>
    </div>
  </body>
</html>