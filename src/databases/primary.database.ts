import { join } from 'path';
import { initConnection } from './helper';
import { configEnv } from '~/@config/env';
import { PRIMARY_CONNECTION_NAME } from './constants';

const {
  DB_PRIMARY_TYPE,
  DB_PRIMARY_HOST,
  DB_PRIMARY_PORT,
  DB_PRIMARY_USERNAME,
  DB_PRIMARY_PASSWORD,
  DB_PRIMARY_DATABASE,
  DB_PRIMARY_SYNCHRONIZE = false,
  DB_PRIMARY_SSL = false,
  DB_PRIMARY_SSL_REJECT_UNAUTHORIZED = true,
} = configEnv();

const primaryDatabase = initConnection({
  name: PRIMARY_CONNECTION_NAME,
  type: DB_PRIMARY_TYPE,
  host: DB_PRIMARY_HOST,
  port: DB_PRIMARY_PORT,
  username: DB_PRIMARY_USERNAME,
  password: DB_PRIMARY_PASSWORD,
  database: DB_PRIMARY_DATABASE,
  synchronize: DB_PRIMARY_SYNCHRONIZE,
  ssl: DB_PRIMARY_SSL,
  sslRejectUnauthorized: DB_PRIMARY_SSL_REJECT_UNAUTHORIZED,
  entities: [join(__dirname, '../domains/primary/**/*.entity{.ts,.js}')],
  logging: ['log', 'error', 'info'],
  //logger: 'simple-console',
});
export { primaryDatabase };
