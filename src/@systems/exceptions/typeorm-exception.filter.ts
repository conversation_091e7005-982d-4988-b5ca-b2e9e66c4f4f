import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { EntityNotFoundError, QueryFailedError } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { ApiException } from './dto';
import { formatException } from './helper';

@Catch(QueryFailedError, EntityNotFoundError)
export class TypeOrmFilter implements ExceptionFilter {
    constructor(private readonly i18n: I18nService) {}

    catch(exception: QueryFailedError | EntityNotFoundError, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();

        const status = HttpStatus.BAD_REQUEST;
        const apiException = new ApiException('Unknown', status, {
            message: exception.message,
        });

        response.status(status).send(formatException(apiException, this.i18n));
    }
}
